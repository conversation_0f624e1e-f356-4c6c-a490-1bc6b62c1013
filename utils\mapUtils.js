/**
 * 地图工具函数集合
 * 提供坐标转换、数据格式化等通用功能
 */

/**
 * 解析位置字符串为经纬度对象
 * @param {string|undefined|null} location - 位置字符串，格式："经度,纬度"
 * @returns {object} 包含经度和纬度的对象
 */
export function parseLocation(location) {
  // 处理各种无效输入情况
  if (!location || typeof location !== 'string' || location.trim() === '') {
    console.warn('位置信息格式错误:', location)
    return { longitude: 0, latitude: 0 }
  }

  // 去除首尾空格并分割
  const trimmedLocation = location.trim()
  const parts = trimmedLocation.split(',')

  // 检查是否有两个部分（经度和纬度）
  if (parts.length !== 2) {
    console.warn('位置信息格式错误，应为"经度,纬度"格式:', location)
    return { longitude: 0, latitude: 0 }
  }

  const [longitudeStr, latitudeStr] = parts
  const longitude = parseFloat(longitudeStr.trim())
  const latitude = parseFloat(latitudeStr.trim())

  // 检查解析结果是否为有效数字
  if (isNaN(longitude) || isNaN(latitude)) {
    console.warn('位置信息包含无效数字:', location)
    return { longitude: 0, latitude: 0 }
  }

  // 检查经纬度范围是否合理
  if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
    console.warn('位置信息超出有效范围:', location)
    return { longitude: 0, latitude: 0 }
  }

  return {
    longitude,
    latitude
  }
}

/**
 * 创建地图标记点数据
 * @param {object} options - 标记点配置
 * @returns {object} 标记点数据对象
 */
export function createMarker(options) {
  const {
    id = 0,
    location,
    iconPath = '/static/marker.png',
    width = 23,
    height = 33,
    name = '',
    address = ''
  } = options
  
  const { longitude, latitude } = parseLocation(location)
  
  return {
    id,
    latitude,
    longitude,
    iconPath,
    width,
    height,
    name,
    address
  }
}

/**
 * 创建起点标记
 * @param {object} locationInfo - 位置信息
 * @returns {object} 起点标记数据
 */
export function createStartMarker(locationInfo) {
  return createMarker({
    id: 0,
    location: locationInfo.location,
    iconPath: '/static/mapicon_navi_s.png',
    name: locationInfo.name || '起点',
    address: locationInfo.district || '当前位置'
  })
}

/**
 * 创建终点标记
 * @param {object} locationInfo - 位置信息
 * @returns {object} 终点标记数据
 */
export function createEndMarker(locationInfo) {
  return createMarker({
    id: 1,
    location: locationInfo.location,
    iconPath: '/static/mapicon_navi_e.png',
    width: 24,
    height: 34,
    name: locationInfo.name || '终点',
    address: locationInfo.district || '目的地'
  })
}

/**
 * 解析路线数据为地图可用的点集合
 * @param {array} steps - 路线步骤数组
 * @returns {array} 路线点集合
 */
export function parseRoutePoints(steps) {
  const points = []
  
  if (!Array.isArray(steps)) {
    console.warn('路线步骤数据格式错误:', steps)
    return points
  }
  
  steps.forEach(step => {
    if (step.polyline) {
      const polylinePoints = step.polyline.split(';')
      polylinePoints.forEach(point => {
        const [lng, lat] = point.split(',')
        if (lng && lat) {
          points.push({
            longitude: parseFloat(lng),
            latitude: parseFloat(lat)
          })
        }
      })
    }
  })
  
  return points
}

/**
 * 解析骑行路线数据
 * @param {array} rides - 骑行路段数组
 * @returns {array} 路线点集合
 */
export function parseRidingPoints(rides) {
  const points = []
  
  if (!Array.isArray(rides)) {
    console.warn('骑行路线数据格式错误:', rides)
    return points
  }
  
  rides.forEach(ride => {
    if (ride.polyline) {
      const polylinePoints = ride.polyline.split(';')
      polylinePoints.forEach(point => {
        const [lng, lat] = point.split(',')
        if (lng && lat) {
          points.push({
            longitude: parseFloat(lng),
            latitude: parseFloat(lat)
          })
        }
      })
    }
  })
  
  return points
}

/**
 * 创建路线样式配置
 * @param {array} points - 路线点集合
 * @param {string} color - 路线颜色，默认蓝色
 * @param {number} width - 路线宽度，默认6
 * @returns {array} 路线配置数组
 */
export function createPolyline(points, color = '#0091ff', width = 6) {
  return [{
    points,
    color,
    width
  }]
}

/**
 * 格式化距离显示
 * @param {number} distance - 距离（米）
 * @returns {string} 格式化后的距离字符串
 */
export function formatDistance(distance) {
  if (!distance || isNaN(distance)) return '0米'
  
  const distanceNum = parseInt(distance)
  if (distanceNum >= 1000) {
    return (distanceNum / 1000).toFixed(1) + '公里'
  }
  return distanceNum + '米'
}

/**
 * 格式化时间显示
 * @param {number} duration - 时长（秒）
 * @returns {string} 格式化后的时间字符串
 */
export function formatDuration(duration) {
  if (!duration || isNaN(duration)) return '0分钟'
  
  const minutes = Math.round(duration / 60)
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }
  return minutes + '分钟'
}

/**
 * 格式化打车费用显示
 * @param {number} cost - 费用
 * @returns {string} 格式化后的费用字符串
 */
export function formatTaxiCost(cost) {
  if (!cost || isNaN(cost)) return ''
  return '打车约' + parseInt(cost) + '元'
}

/**
 * 处理公交路线数据
 * @param {array} transits - 公交路线数组
 * @returns {array} 处理后的公交路线数据
 */
export function processTransitData(transits) {
  if (!Array.isArray(transits)) {
    console.warn('公交路线数据格式错误:', transits)
    return []
  }
  
  return transits.map(transit => {
    const segments = transit.segments || []
    const transport = []
    
    segments.forEach((segment, index) => {
      if (segment.bus && segment.bus.buslines && segment.bus.buslines[0] && segment.bus.buslines[0].name) {
        let name = segment.bus.buslines[0].name
        if (index !== 0) {
          name = '--' + name
        }
        transport.push(name)
      }
    })
    
    return {
      ...transit,
      transport
    }
  })
}

/**
 * 创建地图包含点配置
 * @param {object} startLocation - 起点位置
 * @param {object} endLocation - 终点位置（可选）
 * @returns {array} 包含点配置数组
 */
export function createIncludePoints(startLocation, endLocation = null) {
  const points = []
  
  if (startLocation) {
    const { longitude, latitude } = parseLocation(startLocation)
    points.push({ latitude, longitude })
  }
  
  if (endLocation) {
    const { longitude, latitude } = parseLocation(endLocation)
    points.push({ latitude, longitude })
  }
  
  return points
}

/**
 * 验证POI标记点数据的有效性
 * @param {object} marker - 标记点数据
 * @returns {boolean} 是否有效
 */
export function validateMarkerData(marker) {
  if (!marker) {
    console.warn('标记点数据为空')
    return false
  }

  // 检查经纬度是否存在且为有效数字
  if (typeof marker.latitude !== 'number' || typeof marker.longitude !== 'number') {
    console.warn('标记点坐标不是数字类型:', marker)
    return false
  }

  if (isNaN(marker.latitude) || isNaN(marker.longitude)) {
    console.warn('标记点坐标为NaN:', marker)
    return false
  }

  // 检查坐标范围是否合理
  if (marker.latitude < -90 || marker.latitude > 90 ||
      marker.longitude < -180 || marker.longitude > 180) {
    console.warn('标记点坐标超出有效范围:', marker)
    return false
  }

  // 排除零坐标（通常表示无效位置）
  if (marker.latitude === 0 && marker.longitude === 0) {
    console.warn('标记点坐标为零:', marker)
    return false
  }

  return true
}

/**
 * 安全地过滤和格式化标记点数据
 * @param {array} markers - 原始标记点数组
 * @returns {array} 过滤后的有效标记点数组
 */
export function safeFilterMarkers(markers) {
  if (!Array.isArray(markers)) {
    console.warn('标记点数据不是数组:', markers)
    return []
  }

  return markers.filter(validateMarkerData)
}

/**
 * 统一的错误处理函数
 * @param {string} operation - 操作名称
 * @param {object} error - 错误信息
 */
export function handleMapError(operation, error) {
  console.error(`${operation}失败:`, error)

  // 显示用户友好的错误提示
  const errorMessage = error.errMsg || error.message || '操作失败，请重试'
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  })
}
