"use strict";const e=require("../../common/vendor.js"),S=require("../../composables/useLocationService.js");require("../../libs/amap-wx.130.js");require("../../libs/config.js");require("../../utils/mapUtils.js");Array||e.resolveComponent("uni-easyinput")();const b=()=>"../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js";Math||b();const q={__name:"InputTip",props:{city:{type:String,default:""},longitude:{type:[String,Number],default:""},latitude:{type:[String,Number],default:""},inputType:{type:String,default:"start",validator:r=>["start","end"].includes(r)},defaultValue:{type:String,default:"请输入地点"}},emits:["customEvent"],setup(r,{emit:c}){const o=r,{getInputTips:v}=S.useLocationService(),a=e.ref([]),l=e.ref(!1),p=e.ref(!1),i=e.ref("");let f=null;const d=e.computed(()=>{const n=o.inputType==="start"?"起点":"终点";return o.defaultValue||`请输入${n}`}),m=e.computed(()=>!o.longitude||!o.latitude?"":`${o.longitude},${o.latitude}`);function y(n){var u,s;const t=(typeof n=="string"?n:((u=n.detail)==null?void 0:u.value)||((s=n.target)==null?void 0:s.value)||"").trim();if(console.log("输入内容:",t),i.value=t,f&&clearTimeout(f),!t){a.value=[],l.value=!1,c("customEvent",{info:null,inputType:o.inputType});return}f=setTimeout(()=>{g(t)},300)}function g(n){n.trim()&&(console.log("搜索地点提示:",n),v({keywords:n.trim(),location:m.value,city:o.city},t=>{console.log("搜索提示结果:",t),t&&t.tips?(a.value=t.tips,l.value=!0):(a.value=[],l.value=!0)},t=>{console.error("搜索提示失败:",t),a.value=[],l.value=!1}))}function _(n){const t=n.currentTarget.dataset.info;if(console.log("选择地点:",t),!t){console.warn("未找到地点信息");return}i.value=t.name||"",l.value=!1,c("customEvent",{info:t,inputType:o.inputType})}function T(){console.log("输入框获得焦点"),p.value=!0,a.value.length>0&&(l.value=!0)}function h(){console.log("输入框失去焦点"),p.value=!1,setTimeout(()=>{l.value=!1},200)}function I(){console.log("清除输入内容"),i.value="",a.value=[],l.value=!1,c("customEvent",{info:null,inputType:o.inputType})}return e.watch(()=>o.city,n=>{n&&!i.value&&console.log("城市更新:",n)}),e.onMounted(()=>{console.log(`${o.inputType}搜索框组件已挂载`)}),(n,t)=>e.e({a:e.o(y),b:e.o(T),c:e.o(h),d:e.o(I),e:e.o(u=>i.value=u),f:e.p({placeholder:e.unref(d),clearable:!0,focus:p.value,type:"text",trim:"both",modelValue:i.value}),g:l.value&&a.value.length>0},l.value&&a.value.length>0?{h:e.f(a.value,(u,s,x)=>e.e({a:e.t(u.name),b:u.district},u.district?{c:e.t(u.district)}:{},{d:u.id||u.name||s,e:u,f:u.name,g:e.o(_,u.id||u.name||s)}))}:{},{i:l.value&&a.value.length===0&&i.value},l.value&&a.value.length===0&&i.value?{}:{})}},w=e._export_sfc(q,[["__scopeId","data-v-32c76e58"],["__file","D:/zuomian/前端学习/uniapp项目/GaodeMap/components/InputTip/InputTip.vue"]]);wx.createComponent(w);
