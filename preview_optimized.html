<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公交路线最后连接线优化预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        /* 路线展示 */
        .route-display {
            margin-bottom: 20px;
            padding: 0 10px;
        }
        
        .route-line {
            display: flex;
            align-items: center;
            width: 100%;
            position: relative;
        }
        
        .start-point, .end-point {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #1890ff;
            flex-shrink: 0;
            z-index: 3;
            position: relative;
        }
        
        .route-segments-container {
            display: flex;
            align-items: center;
            flex: 1;
            overflow-x: auto;
            padding: 0 4px;
            min-width: 0;
        }
        
        .segment-with-connector {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }
        
        .route-segment {
            flex-shrink: 0;
            z-index: 2;
            position: relative;
        }
        
        .connector-line {
            width: 12px;
            height: 2px;
            background: #1890ff;
            border-radius: 1px;
            flex-shrink: 0;
            margin: 0 1px;
            z-index: 1;
        }
        
        /* 最后一条动态连接线样式 */
        .final-connector {
            height: 2px;
            background: #1890ff;
            border-radius: 1px;
            flex: 1; /* 自动填充剩余空间 */
            margin-right: 2px; /* 距离终点圆圈2px的间距 */
            margin-left: 1px; /* 与前面内容保持1px间距 */
            z-index: 1;
            min-width: 4px; /* 最小宽度 */
        }
        
        .walking-segment {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 4px 7px;
            border-radius: 8px;
            white-space: nowrap;
        }
        
        .walking-text {
            font-size: 10px;
            color: #6c757d;
        }
        
        .bus-segment {
            background: #1890ff;
            color: white;
            padding: 5px 9px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 60px;
            max-width: 80px;
            box-shadow: 0 1px 2px rgba(24, 144, 255, 0.3);
        }
        
        .bus-name {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 1px;
            text-align: center;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
        
        .station-count {
            font-size: 9px;
            opacity: 0.9;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #ffc107;
        }
        
        .highlight-text {
            font-size: 12px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">最后连接线动态长度优化</div>
        
        <div class="highlight">
            <div class="highlight-text">
                <strong>优化重点：</strong>最后一条连接线现在使用 flex: 1 自动填充剩余空间，
                并保持与终点圆圈 2-4rpx 的美观间距。
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">修改前（固定长度连接线）：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-segments-container">
                        <div class="segment-with-connector">
                            <div class="route-segment">
                                <div class="bus-segment">
                                    <div class="bus-name">地铁1号线</div>
                                    <div class="station-count">3站</div>
                                </div>
                            </div>
                            <div class="connector-line"></div>
                        </div>
                    </div>
                    <!-- 固定长度的连接线 -->
                    <div class="connector-line"></div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">修改后（动态长度连接线）：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-segments-container">
                        <div class="segment-with-connector">
                            <div class="route-segment">
                                <div class="bus-segment">
                                    <div class="bus-name">地铁1号线</div>
                                    <div class="station-count">3站</div>
                                </div>
                            </div>
                            <div class="connector-line"></div>
                        </div>
                    </div>
                    <!-- 动态长度的连接线 -->
                    <div class="final-connector"></div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">复杂路线示例（动态连接线）：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-segments-container">
                        <div class="segment-with-connector">
                            <div class="route-segment">
                                <div class="walking-segment">
                                    <div class="walking-text">步行599米</div>
                                </div>
                            </div>
                            <div class="connector-line"></div>
                        </div>
                        <div class="segment-with-connector">
                            <div class="route-segment">
                                <div class="bus-segment">
                                    <div class="bus-name">地铁1号线</div>
                                    <div class="station-count">3站</div>
                                </div>
                            </div>
                            <div class="connector-line"></div>
                        </div>
                        <div class="segment-with-connector">
                            <div class="route-segment">
                                <div class="walking-segment">
                                    <div class="walking-text">步行230米</div>
                                </div>
                            </div>
                            <div class="connector-line"></div>
                        </div>
                    </div>
                    <!-- 动态长度的最后连接线 -->
                    <div class="final-connector"></div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">短路线示例（展示动态填充效果）：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-segments-container">
                        <div class="segment-with-connector">
                            <div class="route-segment">
                                <div class="walking-segment">
                                    <div class="walking-text">步行99米</div>
                                </div>
                            </div>
                            <div class="connector-line"></div>
                        </div>
                    </div>
                    <!-- 这条线会自动填充剩余空间 -->
                    <div class="final-connector"></div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
