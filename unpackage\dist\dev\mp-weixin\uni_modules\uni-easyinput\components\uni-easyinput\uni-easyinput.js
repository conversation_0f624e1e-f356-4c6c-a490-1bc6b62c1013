"use strict";const l=require("../../../../common/vendor.js");function u(t){let r="";for(let e in t)t[e]&&(r+=`${e} `);return r}function o(t){let r="";for(let e in t){const n=t[e];r+=`${e}:${n};`}return r}const c={name:"uni-easyinput",emits:["click","iconClick","update:modelValue","input","focus","blur","confirm","clear","eyes","change","keyboardheightchange"],model:{prop:"modelValue",event:"update:modelValue"},options:{virtualHost:!0},inject:{form:{from:"uniForm",default:null},formItem:{from:"uniFormItem",default:null}},props:{name:String,value:[Number,String],modelValue:[Number,String],type:{type:String,default:"text"},clearable:{type:Boolean,default:!0},autoHeight:{type:Boolean,default:!1},placeholder:{type:String,default:" "},placeholderStyle:String,focus:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},clearSize:{type:[Number,String],default:24},inputBorder:{type:Boolean,default:!0},prefixIcon:{type:String,default:""},suffixIcon:{type:String,default:""},trim:{type:[Boolean,String],default:!1},cursorSpacing:{type:Number,default:0},passwordIcon:{type:Boolean,default:!0},adjustPosition:{type:Boolean,default:!0},primaryColor:{type:String,default:"#2979ff"},styles:{type:Object,default(){return{color:"#333",backgroundColor:"#fff",disableColor:"#F7F6F6",borderColor:"#e5e5e5"}}},errorMessage:{type:[String,Boolean],default:""}},data(){return{focused:!1,val:"",showMsg:"",border:!1,isFirstBorder:!1,showClearIcon:!1,showPassword:!1,focusShow:!1,localMsg:"",isEnter:!1}},computed:{isVal(){const t=this.val;return!!(t||t===0)},msg(){return this.localMsg||this.errorMessage},inputMaxlength(){return Number(this.maxlength)},boxStyle(){return`color:${this.inputBorder&&this.msg?"#e43d33":this.styles.color};`},inputContentClass(){return u({"is-input-border":this.inputBorder,"is-input-error-border":this.inputBorder&&this.msg,"is-textarea":this.type==="textarea","is-disabled":this.disabled,"is-focused":this.focusShow})},inputContentStyle(){const t=this.focusShow?this.primaryColor:this.styles.borderColor,r=this.inputBorder&&this.msg?"#dd524d":t;return o({"border-color":r||"#e5e5e5","background-color":this.disabled?this.styles.disableColor:this.styles.backgroundColor})},inputStyle(){const t=this.type==="password"||this.clearable||this.prefixIcon?"":"10px";return o({"padding-right":t,"padding-left":this.prefixIcon?"":"10px"})}},watch:{value(t){if(t===null){this.val="";return}this.val=t},modelValue(t){if(t===null){this.val="";return}this.val=t},focus(t){this.$nextTick(()=>{this.focused=this.focus,this.focusShow=this.focus})}},created(){this.init(),this.form&&this.formItem&&this.$watch("formItem.errMsg",t=>{this.localMsg=t})},mounted(){this.$nextTick(()=>{this.focused=this.focus,this.focusShow=this.focus})},methods:{init(){this.value||this.value===0?this.val=this.value:this.modelValue||this.modelValue===0||this.modelValue===""?this.val=this.modelValue:this.val=""},onClickIcon(t){this.$emit("iconClick",t)},onEyes(){this.showPassword=!this.showPassword,this.$emit("eyes",this.showPassword)},onInput(t){let r=t.detail.value;this.trim&&(typeof this.trim=="boolean"&&this.trim&&(r=this.trimStr(r)),typeof this.trim=="string"&&(r=this.trimStr(r,this.trim))),this.errMsg&&(this.errMsg=""),this.val=r,this.$emit("input",r),this.$emit("update:modelValue",r)},onFocus(){this.$nextTick(()=>{this.focused=!0}),this.$emit("focus",null)},_Focus(t){this.focusShow=!0,this.$emit("focus",t)},onBlur(){this.focused=!1,this.$emit("blur",null)},_Blur(t){if(t.detail.value,this.focusShow=!1,this.$emit("blur",t),this.isEnter===!1&&this.$emit("change",this.val),this.form&&this.formItem){const{validateTrigger:r}=this.form;r==="blur"&&this.formItem.onFieldChange()}},onConfirm(t){this.$emit("confirm",this.val),this.isEnter=!0,this.$emit("change",this.val),this.$nextTick(()=>{this.isEnter=!1})},onClear(t){this.val="",this.$emit("input",""),this.$emit("update:modelValue",""),this.$emit("clear")},onkeyboardheightchange(t){this.$emit("keyboardheightchange",t)},trimStr(t,r="both"){return r==="both"?t.trim():r==="left"?t.trimLeft():r==="right"?t.trimRight():r==="start"?t.trimStart():r==="end"?t.trimEnd():r==="all"?t.replace(/\s+/g,""):t}}};Array||l.resolveComponent("uni-icons")();const h=()=>"../../../uni-icons/components/uni-icons/uni-icons.js";Math||h();function f(t,r,e,n,a,i){return l.e({a:e.prefixIcon},e.prefixIcon?{b:l.o(s=>i.onClickIcon("prefix")),c:l.p({type:e.prefixIcon,color:"#c0c4cc",size:"22"})}:{},{d:e.type==="textarea"},e.type==="textarea"?{e:e.inputBorder?1:"",f:e.name,g:a.val,h:e.placeholder,i:e.placeholderStyle,j:e.disabled,k:i.inputMaxlength,l:a.focused,m:e.autoHeight,n:e.cursorSpacing,o:e.adjustPosition,p:l.o((...s)=>i.onInput&&i.onInput(...s)),q:l.o((...s)=>i._Blur&&i._Blur(...s)),r:l.o((...s)=>i._Focus&&i._Focus(...s)),s:l.o((...s)=>i.onConfirm&&i.onConfirm(...s)),t:l.o((...s)=>i.onkeyboardheightchange&&i.onkeyboardheightchange(...s))}:{v:e.type==="password"?"text":e.type,w:l.s(i.inputStyle),x:e.name,y:a.val,z:!a.showPassword&&e.type==="password",A:e.placeholder,B:e.placeholderStyle,C:e.disabled,D:i.inputMaxlength,E:a.focused,F:e.confirmType,G:e.cursorSpacing,H:e.adjustPosition,I:l.o((...s)=>i._Focus&&i._Focus(...s)),J:l.o((...s)=>i._Blur&&i._Blur(...s)),K:l.o((...s)=>i.onInput&&i.onInput(...s)),L:l.o((...s)=>i.onConfirm&&i.onConfirm(...s)),M:l.o((...s)=>i.onkeyboardheightchange&&i.onkeyboardheightchange(...s))},{N:e.type==="password"&&e.passwordIcon},e.type==="password"&&e.passwordIcon?l.e({O:i.isVal},i.isVal?{P:e.type==="textarea"?1:"",Q:l.o(i.onEyes),R:l.p({type:a.showPassword?"eye-slash-filled":"eye-filled",size:22,color:a.focusShow?e.primaryColor:"#c0c4cc"})}:{}):{},{S:e.suffixIcon},e.suffixIcon?l.e({T:e.suffixIcon},e.suffixIcon?{U:l.o(s=>i.onClickIcon("suffix")),V:l.p({type:e.suffixIcon,color:"#c0c4cc",size:"22"})}:{}):l.e({W:e.clearable&&i.isVal&&!e.disabled&&e.type!=="textarea"},e.clearable&&i.isVal&&!e.disabled&&e.type!=="textarea"?{X:e.type==="textarea"?1:"",Y:l.o(i.onClear),Z:l.p({type:"clear",size:e.clearSize,color:i.msg?"#dd524d":a.focusShow?e.primaryColor:"#c0c4cc"})}:{}),{aa:l.n(i.inputContentClass),ab:l.s(i.inputContentStyle),ac:i.msg?1:"",ad:l.s(i.boxStyle)})}const d=l._export_sfc(c,[["render",f],["__file","D:/zuomian/前端学习/uniapp项目/GaodeMap/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue"]]);wx.createComponent(d);
