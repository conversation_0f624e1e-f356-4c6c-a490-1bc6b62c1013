<template>
  <!-- 导航类型选择头部组件 -->
  <view :class="['navigation-header', { 'navigation-active': NavigationOrNot }]">
    <!-- 驾车选项 -->
    <view
      :class="[
        'nav-item',
        {
          'item-active': gaodeType === 'car' && NavigationOrNot,
          'item-disabled': buttonsDisabled
        }
      ]"
      @touchstart="handleCarNavigation"
    >
      <text class="nav-text">🚗 驾车</text>
    </view>

    <!-- 步行选项 -->
    <view
      :class="[
        'nav-item',
        {
          'item-active': gaodeType === 'walk' && NavigationOrNot,
          'item-disabled': buttonsDisabled
        }
      ]"
      @touchstart="handleWalkNavigation"
    >
      <text class="nav-text">🚶 步行</text>
    </view>

    <!-- 公交选项 -->
    <view
      :class="[
        'nav-item',
        {
          'item-active': gaodeType === 'bus' && NavigationOrNot,
          'item-disabled': buttonsDisabled
        }
      ]"
      @touchstart="handleBusNavigation"
    >
      <text class="nav-text">🚌 公交</text>
    </view>

    <!-- 骑行选项 -->
    <view
      :class="[
        'nav-item',
        {
          'item-active': gaodeType === 'riding' && NavigationOrNot,
          'item-disabled': buttonsDisabled
        }
      ]"
      @touchstart="handleRidingNavigation"
    >
      <text class="nav-text">🚴 骑行</text>
    </view>
  </view>
</template>

<script setup>
/**
 * 导航类型选择头部组件
 * 支持驾车、步行、公交、骑行四种导航方式的切换
 * 使用 Vue 3 组合式 API 重构，提供更好的类型安全和代码组织
 */

import { ref, watch, onMounted } from 'vue'

// ===== 组件属性定义 =====

/**
 * 组件接收的属性
 */
const props = defineProps({
  // 当前选中的导航类型
  type: {
    type: String,
    default: 'car',
    validator: (value) => ['car', 'walk', 'bus', 'riding'].includes(value)
  },
  // 是否处于导航状态（影响组件的激活状态）
  NavigationOrNot: {
    type: Boolean,
    default: false
  },
  // 是否禁用所有按钮（当起点或终点为空时）
  buttonsDisabled: {
    type: Boolean,
    default: false
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['changeType'])

// ===== 响应式状态 =====

// 当前选中的导航类型
const gaodeType = ref('car')

// 导航类型配置
const navigationTypes = [
  { key: 'car', label: '驾车', icon: '🚗', description: '适合开车出行' },
  { key: 'walk', label: '步行', icon: '🚶', description: '适合短距离步行' },
  { key: 'bus', label: '公交', icon: '🚌', description: '使用公共交通' },
  { key: 'riding', label: '骑行', icon: '🚴', description: '适合骑自行车' }
]

// ===== 方法定义 =====

/**
 * 初始化组件状态
 */
function initialize() {
  console.log('导航头部组件初始化:', props)

  // 同步父组件传入的导航类型
  if (props.type && props.type !== gaodeType.value) {
    gaodeType.value = props.type
  }
}

/**
 * 切换导航类型的通用方法
 * @param {string} type - 导航类型
 */
function changeNavigationType(type) {
  // 检查按钮是否被禁用
  if (props.buttonsDisabled) {
    console.log('按钮已禁用，请先设置起点和终点')
    uni.showToast({
      title: '请先设置起点和终点',
      icon: 'none',
      duration: 2000
    })
    return
  }

  if (!navigationTypes.find(nav => nav.key === type)) {
    console.warn('无效的导航类型:', type)
    return
  }

  console.log(`切换导航类型: ${type}`)

  // 更新本地状态
  gaodeType.value = type

  // 向父组件发送事件
  emit('changeType', {
    gaode_type: type
  })
}

/**
 * 驾车导航事件处理
 */
function handleCarNavigation() {
  changeNavigationType('car')
}

/**
 * 步行导航事件处理
 */
function handleWalkNavigation() {
  changeNavigationType('walk')
}

/**
 * 公交导航事件处理
 */
function handleBusNavigation() {
  changeNavigationType('bus')
}

/**
 * 骑行导航事件处理
 */
function handleRidingNavigation() {
  changeNavigationType('riding')
}

// ===== 监听器 =====

/**
 * 监听父组件传入的导航类型变化
 */
watch(() => props.type, (newType) => {
  if (newType && newType !== gaodeType.value) {
    console.log('父组件导航类型变化:', newType)
    gaodeType.value = newType
  }
})

/**
 * 监听导航状态变化
 */
watch(() => props.NavigationOrNot, (isNavigating) => {
  console.log('导航状态变化:', isNavigating ? '开始导航' : '停止导航')
})

// ===== 生命周期 =====

/**
 * 组件挂载完成
 */
onMounted(() => {
  initialize()
})
</script>

<style scoped lang="scss">
/**
 * 导航头部组件样式
 * 使用现代CSS布局和设计规范
 */

/* 导航头部容器 */
.navigation-header {
  display: flex;
  width: 100%;
  height: 50px;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  /* 始终保持可见，通过按钮级别控制禁用状态 */
  transition: all 0.3s ease;

  /* 激活状态 */
  &.navigation-active {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 145, 255, 0.15);
  }
}

/* 导航选项 */
.nav-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  /* 悬停效果 */
  &:hover {
    background: rgba(0, 145, 255, 0.05);
  }

  /* 激活状态 */
  &.item-active {
    background: rgba(0, 145, 255, 0.1);
    color: #0091ff;
    font-weight: 600;

    /* 底部指示条 */
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 3px;
      background: #0091ff;
      border-radius: 2px;
    }
  }

  /* 分隔线 */
  &:not(:last-child)::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 60%;
    background: rgba(0, 0, 0, 0.1);
  }
}

/* 导航文本 */
.nav-text {
  font-size: 14px;
  color: #333;
  transition: color 0.2s ease;
  user-select: none;

  .item-active & {
    color: #0091ff;
  }
}

/* 禁用状态样式 */
.item-disabled {
  // opacity: 0.9;
  cursor: not-allowed;
  pointer-events: none;

  &:hover {
    background: transparent !important;
  }

  .nav-text {
    color: #999 !important;
  }

  /* 禁用状态下不显示激活样式 */
  &.item-active {
    background: transparent !important;

    .nav-text {
      color: #999 !important;
    }
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .navigation-header {
    height: 45px;
  }

  .nav-text {
    font-size: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .navigation-header {
    background: #2c2c2c;

    &.navigation-active {
      background: #1a1a1a;
    }
  }

  .nav-text {
    color: #e0e0e0;
  }

  .nav-item:not(:last-child)::before {
    background: rgba(255, 255, 255, 0.2);
  }
}
</style>