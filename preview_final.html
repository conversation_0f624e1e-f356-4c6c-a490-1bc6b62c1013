<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公交路线布局优化最终版</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        /* 路线展示 */
        .route-display {
            margin-bottom: 20px;
            padding: 0 10px;
        }
        
        .route-line {
            display: flex;
            align-items: center;
            width: 100%;
            position: relative;
        }
        
        .start-point, .end-point {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #1890ff;
            flex-shrink: 0;
            z-index: 3;
            position: relative;
        }
        
        /* 路线内容包装容器 */
        .route-content-wrapper {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
        }
        
        .route-segments-container {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            overflow: visible;
            padding: 0 4px;
        }
        
        .segment-with-connector {
            display: flex;
            align-items: center;
            flex-shrink: 0;
        }
        
        .route-segment {
            flex-shrink: 0;
            z-index: 2;
            position: relative;
        }
        
        .connector-line {
            width: 12px;
            height: 2px;
            background: #1890ff;
            border-radius: 1px;
            flex-shrink: 0;
            margin: 0 1px;
            z-index: 1;
        }
        
        /* 最后一条动态连接线样式 */
        .final-connector {
            height: 2px;
            background: #1890ff;
            border-radius: 1px;
            flex: 1;
            margin-right: 2px;
            margin-left: 1px;
            z-index: 1;
            min-width: 12px;
        }
        
        .walking-segment {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 4px 7px;
            border-radius: 8px;
            white-space: nowrap;
        }
        
        .walking-text {
            font-size: 10px;
            color: #6c757d;
        }
        
        .bus-segment {
            background: #1890ff;
            color: white;
            padding: 5px 9px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 60px;
            max-width: 80px;
            box-shadow: 0 1px 2px rgba(24, 144, 255, 0.3);
        }
        
        .bus-name {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 1px;
            text-align: center;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }
        
        .station-count {
            font-size: 9px;
            opacity: 0.9;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .highlight {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #52c41a;
        }
        
        .highlight-text {
            font-size: 12px;
            color: #389e0d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">公交路线布局优化最终版</div>
        
        <div class="highlight">
            <div class="highlight-text">
                <strong>✅ 问题已解决：</strong>
                <br>1. 最后连接线现在紧接着最后一个路线段开始
                <br>2. 路线内容充足时不会出现滚动条
                <br>3. 视觉效果更加自然和连贯
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">短路线（连接线自动填充）：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-content-wrapper">
                        <div class="route-segments-container">
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="bus-segment">
                                        <div class="bus-name">地铁1号线</div>
                                        <div class="station-count">3站</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                        </div>
                        <div class="final-connector"></div>
                    </div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">中等长度路线：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-content-wrapper">
                        <div class="route-segments-container">
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="walking-segment">
                                        <div class="walking-text">步行599米</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="bus-segment">
                                        <div class="bus-name">地铁1号线</div>
                                        <div class="station-count">3站</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                        </div>
                        <div class="final-connector"></div>
                    </div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">长路线（连接线最小宽度）：</div>
            <div class="route-display">
                <div class="route-line">
                    <div class="start-point"></div>
                    <div class="connector-line"></div>
                    <div class="route-content-wrapper">
                        <div class="route-segments-container">
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="walking-segment">
                                        <div class="walking-text">步行599米</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="bus-segment">
                                        <div class="bus-name">地铁1号线</div>
                                        <div class="station-count">3站</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="walking-segment">
                                        <div class="walking-text">步行230米</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                            <div class="segment-with-connector">
                                <div class="route-segment">
                                    <div class="bus-segment">
                                        <div class="bus-name">公交车</div>
                                        <div class="station-count">5站</div>
                                    </div>
                                </div>
                                <div class="connector-line"></div>
                            </div>
                        </div>
                        <div class="final-connector"></div>
                    </div>
                    <div class="end-point"></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
