"use strict";const t=require("../../common/vendor.js"),ie=require("../../composables/useMapState.js"),ue=require("../../composables/useRouteService.js"),se=require("../../composables/useLocationService.js"),re=require("../../utils/mapUtils.js");require("../../libs/amap-wx.130.js");require("../../libs/config.js");Math||(ce+de)();const ce=()=>"../../components/Header/Header.js",de=()=>"../../components/InputTip/InputTip.js",fe={__name:"index",setup(ve){const{mapState:_,markers:c,polyline:w,includePoints:y,latitude:u,longitude:s,city:g,latitude_e:d,longitude_e:f,city_e:m,mapEndObj:k,textData:L,distance:S,cost:O,daohang:h,gaode_type:v,transits:b,currentLocation:$,destinationLocation:A,hasValidRoute:M,setCurrentLocation:E,setDestination:F,setRouteMarkers:j,showMarkerInfo:z,changeMarkerColor:N,resetMapState:Y,changeNavigationType:q}=ie.useMapState(),{planRoute:D}=ue.useRouteService(),{getPoiAround:I}=se.useLocationService(),B=t.computed(()=>{const e=!!(s.value&&u.value),o=!!(f.value&&d.value);return!(e&&o)});let r=[];const P=t.ref(-1);t.onLoad(()=>{G()});function K(e){const o=e.markerId;console.log("点击标记:",o),r&&r.length>0&&(z(r,o),N(r,o))}function W(e){if(console.log("切换导航类型:",e.gaode_type),q(e.gaode_type),!M.value){console.log("缺少有效路线信息，无法规划路线");return}C()}function R(e){console.log("选择位置:",e);const{info:o,inputType:n}=e;if(o===null){n==="start"?Q():n==="end"&&X();return}n==="start"?U(o):n==="end"&&J(o)}function G(){console.log("初始化当前位置"),I({iconPath:"/static/marker.png",iconPathSelected:"/static/marker_checked.png"},e=>{if(console.log("获取当前位置成功:",e),!e){console.warn("获取位置数据为空");return}if(e.markers&&e.markers.length>0){console.log("🎯 处理POI标记点数据，数量:",e.markers.length),console.log("📊 原始POI数据:",e.markers);const o=e.markers.map((n,a)=>{const i={id:n.id!==void 0?n.id:a,latitude:parseFloat(n.latitude),longitude:parseFloat(n.longitude),iconPath:n.iconPath||"/static/marker.png",width:n.width||23,height:n.height||33,name:n.name||`POI-${a}`,address:n.address||"未知地址",callout:{content:n.name||`POI-${a}`,display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5,textAlign:"center"}};return console.log(`📍 格式化POI标记点 ${a}:`,i),i});if(r=o,e.currentLocation&&e.currentLocation.location){console.log("📍 使用处理后的当前位置数据:",e.currentLocation);const n=e.currentLocation.location.split(",");n.length===2&&(s.value=parseFloat(n[0]),u.value=parseFloat(n[1]),g.value=e.currentLocation.name||"",console.log("🗺️ 地图中心已设置:",{longitude:s.value,latitude:u.value,city:g.value}));const a={id:999,latitude:parseFloat(n[1]),longitude:parseFloat(n[0]),iconPath:"/static/marker_checked.png",width:23,height:33,name:e.currentLocation.name||"当前位置",address:e.currentLocation.district||e.currentLocation.address||"",callout:{content:e.currentLocation.name||"当前位置",display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8,textAlign:"center"}},i=[a,...o];c.value=i,console.log("✅ 所有标记点已设置:",{总数量:i.length,当前位置:a,POI数量:o.length,所有标记:i})}else{console.warn("⚠️ 当前位置数据无效，仅显示POI标记点"),c.value=o;const n=o[0];n&&(s.value=n.longitude,u.value=n.latitude,g.value=n.name)}y.value=c.value.filter(n=>n&&n.latitude&&n.longitude).map(n=>({latitude:n.latitude,longitude:n.longitude})),console.log("🗺️ 最终地图状态:",{markersCount:c.value.length,mapState:_.value,longitude:s.value,latitude:u.value,includePointsCount:y.value.length})}else console.warn("未获取到有效的位置标记"),t.index.showToast({title:"无法获取当前位置",icon:"none",duration:3e3})},e=>{console.error("获取当前位置失败:",e),H()})}function H(){console.log("尝试备用方案：直接获取设备位置"),t.index.getLocation({type:"gcj02",success:e=>{console.log("设备位置获取成功:",e),e.latitude,e.longitude,`${e.longitude}${e.latitude}`,s.value=e.longitude,u.value=e.latitude,g.value="当前位置";const o={iconPath:"/static/marker_checked.png",id:999,latitude:e.latitude,longitude:e.longitude,width:23,height:33,name:"当前位置",address:"设备定位",callout:{content:"当前位置",display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8}};if(r&&r.length>0){const n=r.filter(i=>i.id!==999),a=[o,...n];r=a,c.value=a,console.log("🔄 合并设备位置和POI标记点:",a.length)}else r=[o],c.value=[o],console.log("📍 仅显示设备位置标记点");t.index.showToast({title:"已获取设备位置",icon:"success",duration:2e3})},fail:e=>{console.error("设备位置获取失败:",e),V()}})}function V(){console.log("使用默认位置：北京天安门");const e={name:"天安门",address:"北京市东城区东长安街",latitude:39.90923,longitude:116.397428,location:"116.397428,39.90923",district:"东城区"};s.value=e.longitude,u.value=e.latitude,g.value=e.name;const o={iconPath:"/static/marker_checked.png",id:999,latitude:e.latitude,longitude:e.longitude,width:23,height:33,name:e.name,address:e.address,callout:{content:e.name,display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8}},n=[{iconPath:"/static/marker.png",id:1,latitude:39.91023,longitude:116.398428,width:23,height:33,name:"测试POI1",address:"测试地址1",callout:{content:"测试POI1",display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}},{iconPath:"/static/marker.png",id:2,latitude:39.90823,longitude:116.396428,width:23,height:33,name:"测试POI2",address:"测试地址2",callout:{content:"测试POI2",display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}}],a=[o,...n];r=a,c.value=a,console.log("🎯 默认标记点已设置:",{总数量:a.length,当前位置:o,测试POI数量:n.length}),t.index.showToast({title:"已设置默认位置",icon:"none",duration:2e3})}function U(e){if(console.log("设置起点:",e),!e){console.warn("起点位置信息为空"),t.index.showToast({title:"位置信息无效",icon:"none",duration:2e3});return}const o={latitude_e:d.value,longitude_e:f.value,city_e:m.value,mapEndObj:{...k.value}};if(Y(),E(e),o.latitude_e&&o.longitude_e&&(d.value=o.latitude_e,f.value=o.longitude_e,m.value=o.city_e,k.value=o.mapEndObj,h.value=!0,console.log("恢复终点数据:",o)),e.location)try{const n=e.location.split(",");if(n.length===2){const a={iconPath:"/static/marker_checked.png",id:0,latitude:parseFloat(n[1]),longitude:parseFloat(n[0]),width:23,height:33,name:e.name||"起点",address:e.district||e.address||""};r=[a],c.value=[a],console.log("🚗 进入导航模式，显示起点标记，POI标记已清除")}}catch(n){console.error("处理起点标记数据时出错:",n)}M.value&&C()}function J(e){if(console.log("设置终点:",e),!e){console.warn("终点位置信息为空"),t.index.showToast({title:"位置信息无效",icon:"none",duration:2e3});return}F(e),M.value&&C()}function Q(){console.log("🗑️ 清空起点位置");const e={latitude_e:d.value,longitude_e:f.value,city_e:m.value,mapEndObj:{...k.value},daohang:h.value};console.log("💾 保存终点数据:",e),s.value="",u.value="",g.value="",h.value=!1,c.value=[],w.value=[],y.value=[],L.value={},S.value="",O.value="",_.value=!0,b.value=[],e.latitude_e&&e.longitude_e&&(d.value=e.latitude_e,f.value=e.longitude_e,m.value=e.city_e,k.value=e.mapEndObj,console.log("✅ 终点数据已恢复（但导航状态已重置）:",e)),setTimeout(()=>{!s.value&&!u.value&&(console.log("🔄 延迟获取POI数据用于地图显示"),I({iconPath:"/static/marker.png",iconPathSelected:"/static/marker_checked.png"},o=>{if(console.log("🔄 重新获取POI数据用于地图显示"),o&&o.markers&&o.markers.length>0){const a=re.safeFilterMarkers(o.markers).map((i,l)=>({id:i.id!==void 0?i.id:l,latitude:parseFloat(i.latitude),longitude:parseFloat(i.longitude),iconPath:"/static/marker.png",width:23,height:33,name:i.name||`POI-${l}`,address:i.address||"未知地址",callout:{content:i.name||`POI-${l}`,display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}}));r=a,c.value=a,console.log("✅ 仅显示POI标记点，起点已清空:",a.length)}},o=>{console.error("获取POI数据失败:",o),c.value=[],r=[]}))},100),console.log("✅ 起点位置已彻底清空，终点数据保持不变"),setTimeout(()=>{console.log("🔍 清空后的状态检查:",{longitude:s.value,latitude:u.value,longitude_e:f.value,latitude_e:d.value,buttonsDisabled:!s.value||!u.value||!f.value||!d.value})},200)}function X(){console.log("🗑️ 清空终点位置");const e={latitude:u.value,longitude:s.value,city:g.value};console.log("💾 保存起点数据:",e),f.value="",d.value="",m.value="",k.value={},h.value=!1,w.value=[],S.value="",O.value="",b.value=[],L.value={},e.latitude&&e.longitude?(u.value=e.latitude,s.value=e.longitude,g.value=e.city,console.log("✅ 起点数据已恢复:",e),setTimeout(()=>{u.value&&s.value&&(console.log("🔄 延迟恢复POI浏览模式"),I({iconPath:"/static/marker.png",iconPathSelected:"/static/marker_checked.png"},o=>{if(console.log("🔄 恢复POI浏览模式"),o&&o.markers&&o.markers.length>0){const n=o.markers.filter(l=>l?typeof l.latitude!="number"||typeof l.longitude!="number"?(console.warn("发现非数字坐标的标记点:",l),!1):isNaN(l.latitude)||isNaN(l.longitude)?(console.warn("发现NaN坐标的标记点:",l),!1):l.latitude===0&&l.longitude===0?(console.warn("发现零坐标的标记点:",l),!1):!0:(console.warn("发现空的标记点数据"),!1)).map((l,p)=>({id:l.id!==void 0?l.id:p,latitude:parseFloat(l.latitude),longitude:parseFloat(l.longitude),iconPath:"/static/marker.png",width:23,height:33,name:l.name||`POI-${p}`,address:l.address||"未知地址",callout:{content:l.name||`POI-${p}`,display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}})),i=[{id:999,latitude:e.latitude,longitude:e.longitude,iconPath:"/static/marker_checked.png",width:23,height:33,name:e.city||"当前位置",address:"起点位置",callout:{content:e.city||"当前位置",display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8}},...n];r=i,c.value=i,console.log("✅ 恢复POI浏览模式，显示起点和POI:",i.length)}},o=>{console.error("恢复POI数据失败:",o);const n={id:999,latitude:e.latitude,longitude:e.longitude,iconPath:"/static/marker_checked.png",width:23,height:33,name:e.city||"当前位置",address:"起点位置"};c.value=[n],r=[n]}))},100)):console.log("⚠️ 没有起点数据，仅显示POI"),console.log("✅ 终点位置已清空，起点数据保持不变"),setTimeout(()=>{console.log("🔍 终点清空后的状态检查:",{longitude:s.value,latitude:u.value,longitude_e:f.value,latitude_e:d.value,buttonsDisabled:!s.value||!u.value||!f.value||!d.value})},200)}function C(){if(!M.value){console.warn("缺少有效的路线信息");return}console.log(`开始${v.value}路线规划`);const e={origin:$.value,destination:A.value,city:g.value||"广州"};j(),D(v.value,e,Z,x)}function Z(e){console.log("路线规划成功:",e),v.value==="bus"?e.transits&&(b.value=e.transits,_.value=!1):(e.polyline&&(w.value=e.polyline),e.distance&&(S.value=e.distance),e.cost&&(O.value=e.cost),_.value=!0)}function x(e){console.error("路线规划失败:",e),t.index.showToast({title:"路线规划失败，请重试",icon:"none",duration:2e3})}function ee(e){const o=new Date,n=new Date(o.getTime()+e*1e3),a=n.getHours().toString().padStart(2,"0"),i=n.getMinutes().toString().padStart(2,"0");return`${a}:${i}`}function T(e){const o=[];return!e.segments||!Array.isArray(e.segments)||e.segments.forEach(n=>{if(n.walking&&n.walking.distance&&parseInt(n.walking.distance)>0&&o.push({type:"walking",distance:n.walking.distance}),n.bus&&n.bus.buslines&&n.bus.buslines[0]){const a=n.bus.buslines[0];let i=a.name;i.includes("(")&&(i=i.split("(")[0]),o.push({type:"bus",busline:i,stationCount:a.via_num||0})}}),o}function te(e){P.value===e?P.value=-1:P.value=e}function ne(e,o){console.log("选择公交路线:",e,o),t.index.showToast({title:`已选择方案${o+1}`,icon:"success",duration:1500})}return(e,o)=>t.e({a:t.o(W),b:t.p({type:t.unref(v),NavigationOrNot:t.unref(h),buttonsDisabled:t.unref(B)}),c:t.o(R),d:t.p({city:t.unref(g),longitude:t.unref(s),latitude:t.unref(u),defaultValue:"当前位置",inputType:"start"}),e:t.o(R),f:t.p({city:t.unref(m),longitude:t.unref(f),latitude:t.unref(d),defaultValue:"目的地",inputType:"end"}),g:t.unref(v)!=="bus"&&t.unref(_)},t.unref(v)!=="bus"&&t.unref(_)?{h:t.unref(s),i:t.unref(u),j:t.unref(c),k:t.o(K),l:t.unref(w),m:t.unref(y)}:{},{n:t.unref(v)==="bus"&&t.unref(b).length>0},t.unref(v)==="bus"&&t.unref(b).length>0?{o:t.t(t.unref(b).length),p:t.f(t.unref(b),(n,a,i)=>t.e({a:t.t(Math.round(n.duration/60)),b:t.t(ee(n.duration)),c:a===0},a===0?{}:{},{d:n.nightflag==="1"},n.nightflag==="1"?{}:{},{e:t.f(T(n),(l,p,oe)=>t.e({a:l.type==="walking"},l.type==="walking"?{b:t.t(l.distance)}:l.type==="bus"?{d:t.t(l.busline),e:t.t(l.stationCount)}:{},{c:l.type==="bus",f:p<T(n).length-1},p<T(n).length-1?{}:{},{g:p})),f:t.t((n.distance/1e3).toFixed(1)),g:t.t(n.cost),h:t.t(Math.round(n.walking_distance)),i:t.t(P.value===a?"收起详情":"查看详情"),j:t.t(P.value===a?"▲":"▼"),k:t.o(l=>te(a),a),l:P.value===a},P.value===a?{m:t.f(n.segments,(l,p,oe)=>t.e({a:l.walking&&l.walking.distance},l.walking&&l.walking.distance?{b:t.t(l.walking.distance),c:t.t(Math.round(l.walking.duration/60)),d:t.f(l.walking.steps,(le,ae,pe)=>({a:t.t(le.instruction),b:ae}))}:{},{e:l.bus&&l.bus.buslines&&l.bus.buslines[0]},l.bus&&l.bus.buslines&&l.bus.buslines[0]?{f:t.t(l.bus.buslines[0].name),g:t.t(Math.round(l.bus.buslines[0].duration/60)),h:t.t(l.bus.buslines[0].departure_stop.name),i:t.t(l.bus.buslines[0].arrival_stop.name),j:t.t(l.bus.buslines[0].start_time),k:t.t(l.bus.buslines[0].end_time)}:{},{l:p}))}:{},{n:a,o:t.o(l=>ne(n,a),a)}))}:{},{q:t.unref(v)!=="bus"},t.unref(v)!=="bus"?t.e({r:t.t(t.unref(L).name),s:t.t(t.unref(L).desc),t:t.unref(h)},t.unref(h)?{v:t.t(t.unref(S)),w:t.t(t.unref(O))}:{}):{})}},ge=t._export_sfc(fe,[["__file","D:/zuomian/前端学习/uniapp项目/GaodeMap/pages/index/index.vue"]]);wx.createPage(ge);
