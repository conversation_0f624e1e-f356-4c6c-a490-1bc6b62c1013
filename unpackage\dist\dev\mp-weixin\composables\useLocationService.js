"use strict";const P=require("../libs/amap-wx.130.js"),I=require("../libs/config.js"),u=require("../utils/mapUtils.js");function A(){let l=null;function a(){return l||(l=new P.AMapWX({key:I.gaode_key.config.key})),l}function h(i={},c,e){const{keywords:t="",iconPath:d="/static/marker.png",iconPathSelected:r="/static/marker_checked.png"}=i;console.log("开始搜索周边POI:",t||"当前位置");const m=a(),s={iconPath:d,iconPathSelected:r,success:function(n){console.log("POI搜索结果:",n);try{if(n&&n.markers&&n.markers.length>0){const g=u.safeFilterMarkers(n.markers);if(g.length===0){console.warn("没有有效的POI标记点"),e&&e({message:"未找到有效的地点信息"});return}const o=g[0],f={id:o.id,name:o.name||"未知地点",address:o.address||"",latitude:o.latitude,longitude:o.longitude,location:`${o.longitude},${o.latitude}`,iconPath:o.iconPath,width:o.width,height:o.height},p={markers:g,currentLocation:f,poisData:n.poisData||[]};console.log("处理后的位置数据:",p),c&&c(p)}else console.warn("POI搜索无结果"),e&&e({message:"未找到相关地点"})}catch(g){console.error("POI数据处理失败:",g),e&&e(g)}},fail:function(n){console.error("POI搜索失败:",n),u.handleMapError("地点搜索",n),e&&e(n)}};t&&(s.querykeywords=t),m.getPoiAround(s)}function y(i,c,e){const{keywords:t="",location:d="",city:r=""}=i;if(!t.trim()){c&&c({tips:[]});return}a().getInputtips({keywords:t.trim(),location:d,city:r,success:function(s){console.log("输入提示结果:",s);try{s&&s.tips?c&&c({tips:s.tips,keyword:t}):c&&c({tips:[]})}catch(n){console.error("输入提示数据处理失败:",n),e&&e(n)}},fail:function(s){console.error("获取输入提示失败:",s),u.handleMapError("获取搜索提示",s),e&&e(s)}})}function k(i,c="",e,t){if(!i||!i.trim()){console.warn("地理编码缺少地址参数"),t&&t({message:"地址不能为空"});return}console.log("地理编码:",i),a().getGeocode({address:i.trim(),city:c,success:function(r){console.log("地理编码结果:",r);try{if(r&&r.geocodes&&r.geocodes.length>0){const m=r.geocodes[0],s={name:i,location:m.location,district:m.district||c,adcode:m.adcode,formatted_address:m.formatted_address};e&&e(s)}else console.warn("地理编码无结果"),t&&t({message:"未找到该地址"})}catch(m){console.error("地理编码数据处理失败:",m),t&&t(m)}},fail:function(r){console.error("地理编码失败:",r),u.handleMapError("地址解析",r),t&&t(r)}})}function w(i,c,e){if(!i||!i.includes(",")){console.warn("逆地理编码缺少有效坐标"),e&&e({message:"坐标格式错误"});return}a().getRegeocode({location:i,success:function(d){var r,m,s,n,g;console.log("逆地理编码结果:",d);try{if(d&&d.regeocodes&&d.regeocodes.length>0){const o=d.regeocodes[0],f={formatted_address:o.formatted_address,province:((r=o.addressComponent)==null?void 0:r.province)||"",city:((m=o.addressComponent)==null?void 0:m.city)||"",district:((s=o.addressComponent)==null?void 0:s.district)||"",street:((n=o.addressComponent)==null?void 0:n.street)||"",streetNumber:((g=o.addressComponent)==null?void 0:g.streetNumber)||"",location:i};c&&c(f)}else console.warn("逆地理编码无结果"),e&&e({message:"无法解析该坐标"})}catch(o){console.error("逆地理编码数据处理失败:",o),e&&e(o)}},fail:function(d){console.error("逆地理编码失败:",d),u.handleMapError("坐标解析",d),e&&e(d)}})}return{getPoiAround:h,getInputTips:y,getGeocode:k,getRegeocode:w}}exports.useLocationService=A;
