<template>
  <!-- 导航类型选择头部 -->
  <GaodeHeader
    class="noDaohang"
    @changeType="handleNavigationTypeChange"
    :type="gaode_type"
    :NavigationOrNot="daohang"
    :buttonsDisabled="buttonsDisabled"
  />

  <!-- 搜索输入区域 -->
  <view class="section">
    <!-- 起点输入框 -->
    <GaodeInputTips
      @customEvent="handleLocationSelect"
      :city="city"
      :longitude="longitude"
      :latitude="latitude"
      :defaultValue="'当前位置'"
      inputType="start"
    />

    <!-- 终点输入框 -->
    <GaodeInputTips
      @customEvent="handleLocationSelect"
      :city="city_e"
      :longitude="longitude_e"
      :latitude="latitude_e"
      :defaultValue="'目的地'"
      inputType="end"
    />
  </view>
  <!-- 地图容器 -->
  <view class="map_container">
    <!-- 地图组件 - 非公交模式时显示 -->
    <map
      v-if="gaode_type !== 'bus' && mapState"
      class="map"
      id="map"
      :longitude="longitude"
      :latitude="latitude"
      scale="14"
      show-location="true"
      :markers="markers"
      @markertap="handleMarkerTap"
      :polyline="polyline"
      :include-points="includePoints"
    />

    <!-- 公交路线列表 - 公交模式时显示 -->
    <view v-if="gaode_type === 'bus' && transits.length > 0" class="transit_container">
      <view class="transit-header-title">
        <text class="title-text">公交路线方案</text>
        <text class="count-text">{{ transits.length }}个方案</text>
      </view>

      <view class="transit-list">
        <view
          v-for="(transit, index) in transits"
          :key="index"
          class="transit-item"
          @click="selectTransit(transit, index)"
        >
          <!-- 路线头部 -->
          <view class="route-header">
            <view class="time-info">
              <text class="duration">{{ Math.round(transit.duration / 60) }}分钟</text>
              <text class="arrival">{{ getArrivalTime(transit.duration) }}到达</text>
            </view>
            <view class="tags">
              <text v-if="index === 0" class="recommend-tag">推荐</text>
              <text v-if="transit.nightflag === '1'" class="night-tag">夜班</text>
            </view>
          </view>

          <!-- 路线展示 -->
          <view class="route-display">
            <view class="route-line">
              <view class="start-point"></view>

              <!-- 起点后的连接线 -->
              <view class="connector-line"></view>

              <!-- 路线段和连接线 -->
              <view class="route-segments-container">
                <view
                  v-for="(segment, segIndex) in getSimpleSegments(transit)"
                  :key="segIndex"
                  class="segment-with-connector"
                >
                  <!-- 路线段 -->
                  <view class="route-segment">
                    <view v-if="segment.type === 'walking'" class="walking-segment">
                      <text class="walking-text">步行{{ segment.distance }}米</text>
                    </view>
                    <view v-else-if="segment.type === 'bus'" class="bus-segment">
                      <text class="bus-name">{{ segment.busline }}</text>
                      <text class="station-count">{{ segment.stationCount }}站</text>
                    </view>
                  </view>

                  <!-- 连接线（在段之后） -->
                  <view class="connector-line"></view>
                </view>
              </view>

              <!-- 最后的动态连接线到终点 -->
              <view class="final-connector"></view>

              <view class="end-point"></view>
            </view>
          </view>

          <!-- 路线信息 -->
          <view class="route-info">
            <view class="info-item">
              <text class="info-label">距离</text>
              <text class="info-value">{{ (transit.distance / 1000).toFixed(1) }}公里</text>
            </view>
            <view class="info-item">
              <text class="info-label">费用</text>
              <text class="info-value">{{ transit.cost }}元</text>
            </view>
            <view class="info-item">
              <text class="info-label">步行</text>
              <text class="info-value">{{ Math.round(transit.walking_distance) }}米</text>
            </view>
          </view>

          <!-- 展开详情 -->
          <view class="expand-btn" @click.stop="toggleDetails(index)">
            <text class="expand-text">{{ expandedIndex === index ? '收起详情' : '查看详情' }}</text>
            <text class="expand-icon">{{ expandedIndex === index ? '▲' : '▼' }}</text>
          </view>

          <!-- 详细信息 -->
          <view v-if="expandedIndex === index" class="route-details">
            <view
              v-for="(segment, segIndex) in transit.segments"
              :key="segIndex"
              class="detail-item"
            >
              <!-- 步行详情 -->
              <view v-if="segment.walking && segment.walking.distance" class="walking-detail">
                <view class="detail-header">
                  <text class="detail-icon">🚶</text>
                  <text class="detail-title">步行 {{ segment.walking.distance }}米</text>
                  <text class="detail-time">约{{ Math.round(segment.walking.duration / 60) }}分钟</text>
                </view>
                <view class="walking-steps">
                  <text
                    v-for="(step, stepIndex) in segment.walking.steps"
                    :key="stepIndex"
                    class="step-text"
                  >
                    {{ step.instruction }}
                  </text>
                </view>
              </view>

              <!-- 公交详情 -->
              <view v-if="segment.bus && segment.bus.buslines && segment.bus.buslines[0]" class="bus-detail">
                <view class="detail-header">
                  <text class="detail-icon">🚌</text>
                  <text class="detail-title">{{ segment.bus.buslines[0].name }}</text>
                  <text class="detail-time">{{ Math.round(segment.bus.buslines[0].duration / 60) }}分钟</text>
                </view>

                <view class="bus-stations">
                  <view class="station-row">
                    <text class="station-label">上车：</text>
                    <text class="station-name">{{ segment.bus.buslines[0].departure_stop.name }}</text>
                  </view>
                  <view class="station-row">
                    <text class="station-label">下车：</text>
                    <text class="station-name">{{ segment.bus.buslines[0].arrival_stop.name }}</text>
                  </view>
                  <view class="station-row">
                    <text class="station-label">运营：</text>
                    <text class="station-name">{{ segment.bus.buslines[0].start_time }}-{{ segment.bus.buslines[0].end_time }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息显示区域 -->
  <view class="map_text" v-if="gaode_type !== 'bus'">
    <!-- 水平布局的文本信息容器 -->
    <view class="text_info_horizontal">
      <text class="h1">{{ textData.name }}</text>
      <text class="desc_text">{{ textData.desc }}</text>
    </view>

    <!-- 导航信息 - 有路线时显示 -->
    <view class="text_box" v-if="daohang">
      <view class="text">{{ distance }}</view>
      <view class="text">{{ cost }}</view>
    </view>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'

// 导入自定义组件
import GaodeHeader from '../../components/Header/Header.vue'
import GaodeInputTips from '../../components/InputTip/InputTip.vue'

// 导入组合式函数
import { useMapState } from '../../composables/useMapState.js'
import { useRouteService } from '../../composables/useRouteService.js'
import { useLocationService } from '../../composables/useLocationService.js'

// 导入工具函数
import { safeFilterMarkers } from '../../utils/mapUtils.js'

// ===== 组合式函数初始化 =====

// 地图状态管理
const {
  // 状态
  mapState,
  markers,
  polyline,
  includePoints,
  latitude,
  longitude,
  city,
  latitude_e,
  longitude_e,
  city_e,
  mapEndObj,
  textData,
  distance,
  cost,
  daohang,
  gaode_type,
  transits,

  // 计算属性
  currentLocation,
  destinationLocation,
  hasValidRoute,

  // 方法
  setCurrentLocation,
  setDestination,
  setRouteMarkers,
  showMarkerInfo,
  changeMarkerColor,
  resetMapState,
  changeNavigationType
} = useMapState()

// 路线服务
const { planRoute } = useRouteService()

// 位置服务
const { getPoiAround } = useLocationService()

// ===== 计算属性 =====

/**
 * 判断功能按钮是否应该被禁用
 * 当起点或终点任意一个为空时，禁用所有功能按钮
 */
const buttonsDisabled = computed(() => {
  // 检查起点：需要有经纬度坐标
  const hasStartPoint = !!(longitude.value && latitude.value)

  // 检查终点：需要有经纬度坐标
  const hasEndPoint = !!(longitude_e.value && latitude_e.value)

  // 只有当起点和终点都存在时，才启用按钮
  const isEnabled = hasStartPoint && hasEndPoint
  return !isEnabled
})

// 全局变量 - 保存原始标记数据
let markersData = []

// ===== 响应式数据 =====
const expandedIndex = ref(-1) // 当前展开的路线详情索引

// ===== 事件处理函数 =====

/**
 * 页面加载完成事件
 */
onLoad(() => {
  initializeCurrentLocation()
})

/**
 * 地图标记点击事件处理
 * @param {object} e - 点击事件对象
 */
function handleMarkerTap(e) {
  const markerId = e.markerId
  console.log('点击标记:', markerId)

  if (markersData && markersData.length > 0) {
    showMarkerInfo(markersData, markerId)
    changeMarkerColor(markersData, markerId)
  }
}

/**
 * 导航类型切换事件处理
 * @param {object} data - 导航类型数据
 */
function handleNavigationTypeChange(data) {
  console.log('切换导航类型:', data.gaode_type)

  // 更新导航类型
  changeNavigationType(data.gaode_type)

  // 如果没有目的地，直接返回
  if (!hasValidRoute.value) {
    console.log('缺少有效路线信息，无法规划路线')
    return
  }

  // 重新规划路线
  planRouteWithCurrentType()
}

/**
 * 位置选择事件处理
 * @param {object} data - 位置选择数据
 */
function handleLocationSelect(data) {
  console.log('选择位置:', data)

  const { info, inputType } = data

  // 如果info为null，表示清空操作
  if (info === null) {
    if (inputType === 'start') {
      // 清空起点
      handleClearStartLocation()
    } else if (inputType === 'end') {
      // 清空终点
      handleClearEndLocation()
    }
    return
  }

  if (inputType === 'start') {
    // 设置起点
    handleStartLocationSelect(info)
  } else if (inputType === 'end') {
    // 设置终点
    handleEndLocationSelect(info)
  }
}

// ===== 业务逻辑函数 =====

/**
 * 初始化当前位置
 */
function initializeCurrentLocation() {
  console.log('初始化当前位置')

  getPoiAround(
    {
      iconPath: '/static/marker.png',
      iconPathSelected: '/static/marker_checked.png'
    },
    (data) => {
      console.log('获取当前位置成功:', data)

      // 验证返回的数据
      if (!data) {
        console.warn('获取位置数据为空')
        return
      }

      if (data.markers && data.markers.length > 0) {
        console.log('🎯 处理POI标记点数据，数量:', data.markers.length)
        console.log('📊 原始POI数据:', data.markers)

        // 🔧 格式化POI标记点数据，确保符合uni-app地图组件要求
        const formattedPOIMarkers = data.markers.map((marker, index) => {
          const formattedMarker = {
            id: marker.id !== undefined ? marker.id : index,
            latitude: parseFloat(marker.latitude),
            longitude: parseFloat(marker.longitude),
            iconPath: marker.iconPath || '/static/marker.png', // POI使用蓝色标记
            width: marker.width || 23,
            height: marker.height || 33,
            name: marker.name || `POI-${index}`,
            address: marker.address || '未知地址',
            callout: {
              content: marker.name || `POI-${index}`,
              display: 'BYCLICK',
              fontSize: 12,
              borderRadius: 5,
              bgColor: '#ffffff',
              padding: 5,
              textAlign: 'center'
            }
          }

          console.log(`📍 格式化POI标记点 ${index}:`, formattedMarker)
          return formattedMarker
        })

        // 保存POI数据
        markersData = formattedPOIMarkers

        // 验证当前位置数据并设置地图中心
        if (data.currentLocation && data.currentLocation.location) {
          console.log('📍 使用处理后的当前位置数据:', data.currentLocation)

          // 🔧 只设置地图中心位置，不覆盖POI标记点
          const locationParts = data.currentLocation.location.split(',')
          if (locationParts.length === 2) {
            longitude.value = parseFloat(locationParts[0])
            latitude.value = parseFloat(locationParts[1])
            city.value = data.currentLocation.name || ''

            console.log('🗺️ 地图中心已设置:', {
              longitude: longitude.value,
              latitude: latitude.value,
              city: city.value
            })
          }

          // 🎯 创建当前位置标记（红色）
          const currentLocationMarker = {
            id: 999, // 使用特殊ID避免与POI冲突
            latitude: parseFloat(locationParts[1]),
            longitude: parseFloat(locationParts[0]),
            iconPath: '/static/marker_checked.png', // 当前位置使用红色标记
            width: 23,
            height: 33,
            name: data.currentLocation.name || '当前位置',
            address: data.currentLocation.district || data.currentLocation.address || '',
            callout: {
              content: data.currentLocation.name || '当前位置',
              display: 'ALWAYS', // 当前位置始终显示标注
              fontSize: 14,
              borderRadius: 5,
              bgColor: '#ff0000',
              color: '#ffffff',
              padding: 8,
              textAlign: 'center'
            }
          }

          // 🔥 关键修复：合并当前位置标记和POI标记，而不是覆盖
          const allMarkers = [currentLocationMarker, ...formattedPOIMarkers]
          markers.value = allMarkers

          console.log('✅ 所有标记点已设置:', {
            总数量: allMarkers.length,
            当前位置: currentLocationMarker,
            POI数量: formattedPOIMarkers.length,
            所有标记: allMarkers
          })

        } else {
          console.warn('⚠️ 当前位置数据无效，仅显示POI标记点')

          // 如果没有当前位置数据，只显示POI标记点
          markers.value = formattedPOIMarkers

          // 使用第一个POI作为地图中心
          const firstMarker = formattedPOIMarkers[0]
          if (firstMarker) {
            longitude.value = firstMarker.longitude
            latitude.value = firstMarker.latitude
            city.value = firstMarker.name
          }
        }

        // 设置包含点，确保地图能显示所有标记
        includePoints.value = markers.value
          .filter(marker => marker && marker.latitude && marker.longitude) // 过滤无效标记
          .map(marker => ({
            latitude: marker.latitude,
            longitude: marker.longitude
          }))

        console.log('🗺️ 最终地图状态:', {
          markersCount: markers.value.length,
          mapState: mapState.value,
          longitude: longitude.value,
          latitude: latitude.value,
          includePointsCount: includePoints.value.length
        })

      } else {
        console.warn('未获取到有效的位置标记')
        // 显示友好的错误提示
        uni.showToast({
          title: '无法获取当前位置',
          icon: 'none',
          duration: 3000
        })
      }
    },
    (error) => {
      console.error('获取当前位置失败:', error)

      // 尝试备用方案：直接获取设备位置
      tryGetDeviceLocation()
    }
  )
}

/**
 * 备用方案：尝试直接获取设备位置
 */
function tryGetDeviceLocation() {
  console.log('尝试备用方案：直接获取设备位置')

  uni.getLocation({
    type: 'gcj02', // 高德地图坐标系
    success: (res) => {
      console.log('设备位置获取成功:', res)

      // 构造位置信息
      const deviceLocation = {
        name: '当前位置',
        address: '设备定位',
        latitude: res.latitude,
        longitude: res.longitude,
        location: `${res.longitude},${res.latitude}`,
        district: '当前区域'
      }

      // 🔧 只设置地图中心，不覆盖现有的POI标记点
      longitude.value = res.longitude
      latitude.value = res.latitude
      city.value = '当前位置'

      // 创建当前位置标记点
      const deviceMarker = {
        iconPath: '/static/marker_checked.png',
        id: 999, // 使用特殊ID避免与POI冲突
        latitude: res.latitude,
        longitude: res.longitude,
        width: 23,
        height: 33,
        name: '当前位置',
        address: '设备定位',
        callout: {
          content: '当前位置',
          display: 'ALWAYS',
          fontSize: 14,
          borderRadius: 5,
          bgColor: '#ff0000',
          color: '#ffffff',
          padding: 8
        }
      }

      // 🔥 关键修复：检查是否已有POI标记点，如果有则合并，否则只显示当前位置
      if (markersData && markersData.length > 0) {
        // 过滤掉旧的当前位置标记（ID=999），保留POI标记
        const poiMarkers = markersData.filter(marker => marker.id !== 999)
        const allMarkers = [deviceMarker, ...poiMarkers]
        markersData = allMarkers
        markers.value = allMarkers
        console.log('🔄 合并设备位置和POI标记点:', allMarkers.length)
      } else {
        // 没有POI数据，只显示当前位置
        markersData = [deviceMarker]
        markers.value = [deviceMarker]
        console.log('📍 仅显示设备位置标记点')
      }

      uni.showToast({
        title: '已获取设备位置',
        icon: 'success',
        duration: 2000
      })
    },
    fail: (error) => {
      console.error('设备位置获取失败:', error)

      // 最后的备用方案：使用默认位置（北京天安门）
      useDefaultLocation()
    }
  })
}

/**
 * 最后的备用方案：使用默认位置
 */
function useDefaultLocation() {
  console.log('使用默认位置：北京天安门')

  const defaultLocation = {
    name: '天安门',
    address: '北京市东城区东长安街',
    latitude: 39.90923,
    longitude: 116.397428,
    location: '116.397428,39.90923',
    district: '东城区'
  }

  // 🔧 只设置地图中心，不直接操作markers
  longitude.value = defaultLocation.longitude
  latitude.value = defaultLocation.latitude
  city.value = defaultLocation.name

  // 创建当前位置标记点
  const currentLocationMarker = {
    iconPath: '/static/marker_checked.png',
    id: 999, // 使用特殊ID避免与POI冲突
    latitude: defaultLocation.latitude,
    longitude: defaultLocation.longitude,
    width: 23,
    height: 33,
    name: defaultLocation.name,
    address: defaultLocation.address,
    callout: {
      content: defaultLocation.name,
      display: 'ALWAYS',
      fontSize: 14,
      borderRadius: 5,
      bgColor: '#ff0000',
      color: '#ffffff',
      padding: 8
    }
  }

  // 创建测试用的POI标记点
  const testPOIMarkers = [
    {
      iconPath: '/static/marker.png',
      id: 1,
      latitude: 39.91023,
      longitude: 116.398428,
      width: 23,
      height: 33,
      name: '测试POI1',
      address: '测试地址1',
      callout: {
        content: '测试POI1',
        display: 'BYCLICK',
        fontSize: 12,
        borderRadius: 5,
        bgColor: '#ffffff',
        padding: 5
      }
    },
    {
      iconPath: '/static/marker.png',
      id: 2,
      latitude: 39.90823,
      longitude: 116.396428,
      width: 23,
      height: 33,
      name: '测试POI2',
      address: '测试地址2',
      callout: {
        content: '测试POI2',
        display: 'BYCLICK',
        fontSize: 12,
        borderRadius: 5,
        bgColor: '#ffffff',
        padding: 5
      }
    }
  ]

  // 🔥 合并当前位置和测试POI标记点
  const allMarkers = [currentLocationMarker, ...testPOIMarkers]
  markersData = allMarkers
  markers.value = allMarkers

  console.log('🎯 默认标记点已设置:', {
    总数量: allMarkers.length,
    当前位置: currentLocationMarker,
    测试POI数量: testPOIMarkers.length
  })

  uni.showToast({
    title: '已设置默认位置',
    icon: 'none',
    duration: 2000
  })
}

/**
 * 处理起点位置选择
 * @param {object} locationInfo - 位置信息
 */
function handleStartLocationSelect(locationInfo) {
  console.log('设置起点:', locationInfo)

  // 验证位置信息
  if (!locationInfo) {
    console.warn('起点位置信息为空')
    uni.showToast({
      title: '位置信息无效',
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 保存当前的终点数据（如果存在）
  const savedEndData = {
    latitude_e: latitude_e.value,
    longitude_e: longitude_e.value,
    city_e: city_e.value,
    mapEndObj: { ...mapEndObj.value }
  }

  // 重置地图状态
  resetMapState()
  
  // 设置新的起点
  setCurrentLocation(locationInfo)

  // 恢复终点数据（如果之前存在）
  if (savedEndData.latitude_e && savedEndData.longitude_e) {
    latitude_e.value = savedEndData.latitude_e
    longitude_e.value = savedEndData.longitude_e
    city_e.value = savedEndData.city_e
    mapEndObj.value = savedEndData.mapEndObj
    daohang.value = true // 恢复导航状态

    console.log('恢复终点数据:', savedEndData)
  }

  // 如果位置信息有效，创建标记数据
  if (locationInfo.location) {
    try {
      const locationParts = locationInfo.location.split(',')
      if (locationParts.length === 2) {
        const startMarker = {
          iconPath: '/static/marker_checked.png',
          id: 0,
          latitude: parseFloat(locationParts[1]),
          longitude: parseFloat(locationParts[0]),
          width: 23,
          height: 33,
          name: locationInfo.name || '起点',
          address: locationInfo.district || locationInfo.address || ''
        }

        // 🔧 注意：这里进入导航模式，应该显示起点和终点，不再保留POI
        // 导航模式下只显示起点标记，POI标记点会被清除
        markersData = [startMarker]
        markers.value = [startMarker]
        console.log('🚗 进入导航模式，显示起点标记，POI标记已清除')
      }
    } catch (error) {
      console.error('处理起点标记数据时出错:', error)
    }
  }

  // 如果有有效路线，开始规划
  if (hasValidRoute.value) {
    planRouteWithCurrentType()
  }
}

/**
 * 处理终点位置选择
 * @param {object} locationInfo - 位置信息
 */
function handleEndLocationSelect(locationInfo) {
  console.log('设置终点:', locationInfo)

  // 验证位置信息
  if (!locationInfo) {
    console.warn('终点位置信息为空')
    uni.showToast({
      title: '位置信息无效',
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 设置目的地
  setDestination(locationInfo)

  // 如果有有效路线，开始规划
  if (hasValidRoute.value) {
    planRouteWithCurrentType()
  }
}

/**
 * 清空起点位置
 */
function handleClearStartLocation() {
  console.log('🗑️ 清空起点位置')

  // 保存终点数据（避免被清空）
  const savedEndData = {
    latitude_e: latitude_e.value,
    longitude_e: longitude_e.value,
    city_e: city_e.value,
    mapEndObj: { ...mapEndObj.value },
    daohang: daohang.value
  }

  console.log('💾 保存终点数据:', savedEndData)

  // 清空起点坐标数据
  longitude.value = ''
  latitude.value = ''
  city.value = ''

  // 🔥 关键修复：清空导航状态，避免显示底部导航信息和相关错误
  daohang.value = false

  // 清空地图显示状态（但不影响终点数据）
  markers.value = []
  polyline.value = []
  includePoints.value = []
  textData.value = {}
  distance.value = ''
  cost.value = ''
  mapState.value = true

  // �️ 额外保护：清空可能导致错误的其他状态
  transits.value = [] // 清空公交路线数据

  // �🔥 关键修复：恢复终点数据，但不恢复导航状态
  if (savedEndData.latitude_e && savedEndData.longitude_e) {
    latitude_e.value = savedEndData.latitude_e
    longitude_e.value = savedEndData.longitude_e
    city_e.value = savedEndData.city_e
    mapEndObj.value = savedEndData.mapEndObj
    // ⚠️ 注意：不恢复导航状态，因为起点已清空，无法进行导航
    // daohang.value = savedEndData.daohang  // 注释掉这行

    console.log('✅ 终点数据已恢复（但导航状态已重置）:', savedEndData)
  }

  // 🔥 关键修复：添加延迟和错误保护，避免在清空状态下立即调用POI搜索
  setTimeout(() => {
    // 确保在清空状态下才执行POI搜索
    if (!longitude.value && !latitude.value) {
      console.log('🔄 延迟获取POI数据用于地图显示')

      getPoiAround(
        {
          iconPath: '/static/marker.png',
          iconPathSelected: '/static/marker_checked.png'
        },
        (data) => {
          console.log('🔄 重新获取POI数据用于地图显示')

          if (data && data.markers && data.markers.length > 0) {
            // 🔧 使用安全过滤函数格式化POI标记点数据
            const validMarkers = safeFilterMarkers(data.markers)
            const formattedPOIMarkers = validMarkers
              .map((marker, index) => {
                return {
                  id: marker.id !== undefined ? marker.id : index,
                  latitude: parseFloat(marker.latitude),
                  longitude: parseFloat(marker.longitude),
                  iconPath: '/static/marker.png',
                  width: 23,
                  height: 33,
                  name: marker.name || `POI-${index}`,
                  address: marker.address || '未知地址',
                  callout: {
                    content: marker.name || `POI-${index}`,
                    display: 'BYCLICK',
                    fontSize: 12,
                    borderRadius: 5,
                    bgColor: '#ffffff',
                    padding: 5
                  }
                }
              })

            // 只显示POI标记点，不显示当前位置标记
            markersData = formattedPOIMarkers
            markers.value = formattedPOIMarkers

            console.log('✅ 仅显示POI标记点，起点已清空:', formattedPOIMarkers.length)
          }
        },
        (error) => {
          console.error('获取POI数据失败:', error)
          // 🔥 错误时设置空标记数组，避免显示异常
          markers.value = []
          markersData = []
        }
      )
    }
  }, 100) // 添加100ms延迟，确保状态清空完成

  console.log('✅ 起点位置已彻底清空，终点数据保持不变')

  // 🔍 调试：验证按钮状态
  setTimeout(() => {
    console.log('🔍 清空后的状态检查:', {
      longitude: longitude.value,
      latitude: latitude.value,
      longitude_e: longitude_e.value,
      latitude_e: latitude_e.value,
      buttonsDisabled: !longitude.value || !latitude.value || !longitude_e.value || !latitude_e.value
    })

  }, 200)
}

/**
 * 清空终点位置
 */
function handleClearEndLocation() {
  console.log('🗑️ 清空终点位置')

  // 保存起点数据（避免被清空）
  const savedStartData = {
    latitude: latitude.value,
    longitude: longitude.value,
    city: city.value
  }

  console.log('💾 保存起点数据:', savedStartData)

  // 清空终点坐标数据
  longitude_e.value = ''
  latitude_e.value = ''
  city_e.value = ''
  mapEndObj.value = {}

  // 重置导航相关状态
  daohang.value = false
  polyline.value = []
  distance.value = ''
  cost.value = ''
  transits.value = []

  // 🔥 关键修复：清空地图下方显示的文本信息
  textData.value = {}

  // 🔥 关键修复：恢复起点数据
  if (savedStartData.latitude && savedStartData.longitude) {
    latitude.value = savedStartData.latitude
    longitude.value = savedStartData.longitude
    city.value = savedStartData.city

    console.log('✅ 起点数据已恢复:', savedStartData)

    // 🔥 添加延迟和错误保护，确保起点数据恢复后再获取POI
    setTimeout(() => {
      // 确保起点数据已恢复
      if (latitude.value && longitude.value) {
        console.log('🔄 延迟恢复POI浏览模式')

        getPoiAround(
          {
            iconPath: '/static/marker.png',
            iconPathSelected: '/static/marker_checked.png'
          },
          (data) => {
            console.log('🔄 恢复POI浏览模式')

            if (data && data.markers && data.markers.length > 0) {
              // 格式化POI标记点
              const formattedPOIMarkers = data.markers
                .filter(marker => {
                  // 🛡️ 增强数据验证：过滤无效的标记点数据
                  if (!marker) {
                    console.warn('发现空的标记点数据')
                    return false
                  }
                  if (typeof marker.latitude !== 'number' || typeof marker.longitude !== 'number') {
                    console.warn('发现非数字坐标的标记点:', marker)
                    return false
                  }
                  if (isNaN(marker.latitude) || isNaN(marker.longitude)) {
                    console.warn('发现NaN坐标的标记点:', marker)
                    return false
                  }
                  if (marker.latitude === 0 && marker.longitude === 0) {
                    console.warn('发现零坐标的标记点:', marker)
                    return false
                  }
                  return true
                })
                .map((marker, index) => {
                  return {
                    id: marker.id !== undefined ? marker.id : index,
                    latitude: parseFloat(marker.latitude),
                    longitude: parseFloat(marker.longitude),
                    iconPath: '/static/marker.png',
                    width: 23,
                    height: 33,
                    name: marker.name || `POI-${index}`,
                    address: marker.address || '未知地址',
                    callout: {
                      content: marker.name || `POI-${index}`,
                      display: 'BYCLICK',
                      fontSize: 12,
                      borderRadius: 5,
                      bgColor: '#ffffff',
                      padding: 5
                    }
                  }
                })

              // 创建当前位置标记
              const currentLocationMarker = {
                id: 999,
                latitude: savedStartData.latitude,
                longitude: savedStartData.longitude,
                iconPath: '/static/marker_checked.png',
                width: 23,
                height: 33,
                name: savedStartData.city || '当前位置',
                address: '起点位置',
                callout: {
                  content: savedStartData.city || '当前位置',
                  display: 'ALWAYS',
                  fontSize: 14,
                  borderRadius: 5,
                  bgColor: '#ff0000',
                  color: '#ffffff',
                  padding: 8
                }
              }

              // 合并当前位置和POI标记
              const allMarkers = [currentLocationMarker, ...formattedPOIMarkers]
              markersData = allMarkers
              markers.value = allMarkers

              console.log('✅ 恢复POI浏览模式，显示起点和POI:', allMarkers.length)
            }
          },
          (error) => {
            console.error('恢复POI数据失败:', error)
            // 🔥 错误时至少显示当前位置标记
            const currentLocationMarker = {
              id: 999,
              latitude: savedStartData.latitude,
              longitude: savedStartData.longitude,
              iconPath: '/static/marker_checked.png',
              width: 23,
              height: 33,
              name: savedStartData.city || '当前位置',
              address: '起点位置'
            }
            markers.value = [currentLocationMarker]
            markersData = [currentLocationMarker]
          }
        )
      }
    }, 100) // 添加100ms延迟，确保起点数据恢复完成
  } else {
    // 没有起点数据，只显示POI
    console.log('⚠️ 没有起点数据，仅显示POI')
  }

  console.log('✅ 终点位置已清空，起点数据保持不变')

  // 🔍 调试：验证按钮状态
  setTimeout(() => {
    console.log('🔍 终点清空后的状态检查:', {
      longitude: longitude.value,
      latitude: latitude.value,
      longitude_e: longitude_e.value,
      latitude_e: latitude_e.value,
      buttonsDisabled: !longitude.value || !latitude.value || !longitude_e.value || !latitude_e.value
    })
  }, 200)
}

/**
 * 使用当前导航类型规划路线
 */
function planRouteWithCurrentType() {
  if (!hasValidRoute.value) {
    console.warn('缺少有效的路线信息')
    return
  }

  console.log(`开始${gaode_type.value}路线规划`)

  const routeParams = {
    origin: currentLocation.value,
    destination: destinationLocation.value,
    city: city.value || '广州' // 公交路线需要城市参数
  }

  // 设置路线标记点
  setRouteMarkers()

  // 规划路线
  planRoute(
    gaode_type.value,
    routeParams,
    handleRouteSuccess,
    handleRouteError
  )
}

/**
 * 路线规划成功回调
 * @param {object} routeData - 路线数据
 */
function handleRouteSuccess(routeData) {
  console.log('路线规划成功:', routeData)

  if (gaode_type.value === 'bus') {
    // 公交路线处理
    if (routeData.transits) {
      transits.value = routeData.transits
      mapState.value = false // 公交模式显示列表而非地图
    }
  } else {
    // 其他路线类型处理
    if (routeData.polyline) {
      polyline.value = routeData.polyline
    }

    if (routeData.distance) {
      distance.value = routeData.distance
    }

    if (routeData.cost) {
      cost.value = routeData.cost
    }

    mapState.value = true
  }
}

/**
 * 路线规划失败回调
 * @param {object} error - 错误信息
 */
function handleRouteError(error) {
  console.error('路线规划失败:', error)

  uni.showToast({
    title: '路线规划失败，请重试',
    icon: 'none',
    duration: 2000
  })
}

// ===== 公交路线UI相关方法 =====

/**
 * 获取预计到达时间
 * @param {number} duration - 路线总时长（秒）
 * @returns {string} 格式化的到达时间
 */
function getArrivalTime(duration) {
  const now = new Date()
  const arrivalTime = new Date(now.getTime() + duration * 1000)
  const hours = arrivalTime.getHours().toString().padStart(2, '0')
  const minutes = arrivalTime.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

/**
 * 获取简化的路线段信息
 * @param {object} transit - 公交路线数据
 * @returns {array} 简化的路线段数组
 */
function getSimpleSegments(transit) {
  const segments = []

  if (!transit.segments || !Array.isArray(transit.segments)) {
    return segments
  }

  transit.segments.forEach((segment) => {
    // 添加步行段
    if (segment.walking && segment.walking.distance && parseInt(segment.walking.distance) > 0) {
      segments.push({
        type: 'walking',
        distance: segment.walking.distance
      })
    }

    // 添加公交段
    if (segment.bus && segment.bus.buslines && segment.bus.buslines[0]) {
      const busline = segment.bus.buslines[0]
      // 简化公交线路名称
      let simpleName = busline.name
      if (simpleName.includes('(')) {
        simpleName = simpleName.split('(')[0]
      }
      segments.push({
        type: 'bus',
        busline: simpleName,
        stationCount: busline.via_num || 0
      })
    }
  })

  return segments
}

/**
 * 切换路线详情展开状态
 * @param {number} index - 路线索引
 */
function toggleDetails(index) {
  if (expandedIndex.value === index) {
    expandedIndex.value = -1 // 收起
  } else {
    expandedIndex.value = index // 展开
  }
}

/**
 * 选择公交路线
 * @param {object} transit - 选中的路线数据
 * @param {number} index - 路线索引
 */
function selectTransit(transit, index) {
  console.log('选择公交路线:', transit, index)

  // 可以在这里添加选择路线后的逻辑
  // 比如显示详细路线、开始导航等

  uni.showToast({
    title: `已选择方案${index + 1}`,
    icon: 'success',
    duration: 1500
  })
}




</script>

<style scope lang="scss">
/**
 * 页面布局样式
 * 修复地图容器覆盖输入框的问题
 */

/* 搜索输入区域 */
.section {
  z-index: 100;
  background: #f8f9fa;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  padding: 16rpx 20rpx; /* 统一在父容器管理内边距 */
  gap: 8rpx; /* 两个输入框之间的间距 */
}

/* 地图容器 */
.map_container {
  position: absolute;
  top: 280rpx; /* 增加top值，为Header(100rpx) + 两个输入框(约240rpx) 留出空间 */
  bottom: 120rpx; /* 与map_text高度保持一致 */
  left: 0;
  right: 0;
  z-index: 10;
}

/* 地图组件 */
.map {
  width: 100%;
  height: 100%;
  border-radius: 16rpx 16rpx 0 0;
}

/* 底部信息显示区域 */
.map_text {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 120rpx; /* 减少高度，适应水平布局 */
  background: #fff;
  padding: 0 30rpx;
  z-index: 50;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  border-radius: 16rpx 16rpx 0 0;
  overflow: hidden; /* 防止内容溢出 */
}

/* 文本样式 */
text {
  margin: 10rpx 0;
  display: block;
  font-size: 24rpx;
  color: #666;
}

.h1 {
  margin: 0; /* 重置margin，由父容器控制间距 */
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

/* 水平布局的文本信息容器 */
.text_info_horizontal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10rpx 0; /* 减少上下边距 */
  padding: 0 10rpx;
}

/* 描述文本样式 */
.desc_text {
  margin: 0;
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 导航信息文本框 */
.text_box {
  border-bottom: 2rpx solid #e1e5e9;
  font-size: 26rpx;
}

.text_box .text {
  display: inline-block;
  margin-right: 30rpx;
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
  color: #0091ff;
  font-weight: 500;
}

/* 公交列表容器 */
.transit_container {
  background: #fff;
  max-height: 100%;
  overflow-y: auto;
  border-radius: 16rpx;

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 8rpx;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4rpx;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4rpx;

    &:hover {
      background: #a1a1a1;
    }
  }
}

/* 公交路线项 */
.transit_item {
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

/* 公交路线文本 */
.transit_text {
  font-size: 28rpx;
  color: #333;
  line-height: 44rpx;
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}

/* 公交信息行 */
.transit_info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
}

/* 信息文本 */
.info_text {
  font-size: 24rpx;
  color: #666;
  flex: 1;
  text-align: center;
  padding: 8rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

/* ===== 简洁版公交路线样式 ===== */

/* 公交路线容器 */
.transit_container {
  padding: 20rpx;
  background: #f8f9fa;
}

/* 标题区域 */
.transit-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.count-text {
  font-size: 24rpx;
  color: #666;
  background: #e8f4fd;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/* 路线列表 */
.transit-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 单个路线项 */
.transit-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.transit-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 路线头部 */
.route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.time-info {
  display: flex;
  flex-direction: column;
}

.duration {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.2;
}

.arrival {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

.tags {
  display: flex;
  gap: 8rpx;
}

.recommend-tag {
  background: #52c41a;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.night-tag {
  background: #fa8c16;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 路线展示 */
.route-display {
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.route-line {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

.start-point, .end-point {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #1890ff;
  flex-shrink: 0;
  z-index: 3;
  position: relative;
}

.route-segments-container {
  display: flex;
  align-items: center;
  flex: 1;
  overflow-x: auto;
  padding: 0 8rpx;
}

.segment-with-connector {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.route-segment {
  flex-shrink: 0;
  z-index: 2;
  position: relative;
}

.connector-line {
  width: 24rpx;
  height: 3rpx;
  background: #1890ff;
  border-radius: 2rpx;
  flex-shrink: 0;
  margin: 0 2rpx;
  z-index: 1;
}

/* 最后一条动态连接线样式 */
.final-connector {
  height: 3rpx;
  background: #1890ff;
  border-radius: 2rpx;
  flex: 1; /* 自动填充剩余空间 */
  margin-right: 4rpx; /* 距离终点圆圈4rpx的间距 */
  margin-left: 2rpx; /* 与前面内容保持2rpx间距 */
  z-index: 1;
  min-width: 8rpx; /* 最小宽度，确保即使空间很小也有基本显示 */
}

.walking-segment {
  background: #f8f9fa;
  border: 1rpx solid #dee2e6;
  padding: 8rpx 14rpx;
  border-radius: 16rpx;
  white-space: nowrap;
}

.walking-text {
  font-size: 20rpx;
  color: #6c757d;
}

.bus-segment {
  background: #1890ff;
  color: white;
  padding: 10rpx 18rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  max-width: 160rpx;
  box-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.3);
}

.bus-name {
  font-size: 22rpx;
  font-weight: bold;
  margin-bottom: 2rpx;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.station-count {
  font-size: 18rpx;
  opacity: 0.9;
}

/* 路线信息 */
.route-info {
  display: flex;
  justify-content: space-around;
  padding: 16rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.info-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

/* 展开按钮 */
.expand-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 0;
  cursor: pointer;
}

.expand-text {
  font-size: 24rpx;
  color: #1890ff;
}

.expand-icon {
  font-size: 20rpx;
  color: #1890ff;
}

/* 详细信息 */
.route-details {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
  margin-top: 16rpx;
}

.detail-item {
  margin-bottom: 20rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

/* 步行详情 */
.walking-detail {
  .detail-header {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 12rpx;
  }

  .detail-icon {
    font-size: 24rpx;
  }

  .detail-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  .detail-time {
    font-size: 22rpx;
    color: #666;
  }

  .walking-steps {
    padding-left: 32rpx;
  }

  .step-text {
    display: block;
    font-size: 22rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 8rpx;
  }
}

/* 公交详情 */
.bus-detail {
  .detail-header {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-bottom: 16rpx;
  }

  .detail-icon {
    font-size: 24rpx;
  }

  .detail-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  .detail-time {
    font-size: 22rpx;
    color: #666;
  }

  .bus-stations {
    padding-left: 32rpx;
  }

  .station-row {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .station-label {
    font-size: 22rpx;
    color: #666;
    width: 80rpx;
    flex-shrink: 0;
  }

  .station-name {
    font-size: 22rpx;
    color: #333;
    flex: 1;
  }
}




</style>