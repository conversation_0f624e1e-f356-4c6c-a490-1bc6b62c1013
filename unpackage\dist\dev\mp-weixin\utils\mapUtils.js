"use strict";const d=require("../common/vendor.js");function s(t){if(!t||typeof t!="string"||t.trim()==="")return console.warn("位置信息格式错误:",t),{longitude:0,latitude:0};const e=t.trim().split(",");if(e.length!==2)return console.warn('位置信息格式错误，应为"经度,纬度"格式:',t),{longitude:0,latitude:0};const[o,i]=e,a=parseFloat(o.trim()),r=parseFloat(i.trim());return isNaN(a)||isNaN(r)?(console.warn("位置信息包含无效数字:",t),{longitude:0,latitude:0}):a<-180||a>180||r<-90||r>90?(console.warn("位置信息超出有效范围:",t),{longitude:0,latitude:0}):{longitude:a,latitude:r}}function u(t){const{id:n=0,location:e,iconPath:o="/static/marker.png",width:i=23,height:a=33,name:r="",address:l=""}=t,{longitude:c,latitude:f}=s(e);return{id:n,latitude:f,longitude:c,iconPath:o,width:i,height:a,name:r,address:l}}function p(t){return u({id:0,location:t.location,iconPath:"/static/mapicon_navi_s.png",name:t.name||"起点",address:t.district||"当前位置"})}function g(t){return u({id:1,location:t.location,iconPath:"/static/mapicon_navi_e.png",width:24,height:34,name:t.name||"终点",address:t.district||"目的地"})}function h(t){const n=[];return Array.isArray(t)?(t.forEach(e=>{e.polyline&&e.polyline.split(";").forEach(i=>{const[a,r]=i.split(",");a&&r&&n.push({longitude:parseFloat(a),latitude:parseFloat(r)})})}),n):(console.warn("路线步骤数据格式错误:",t),n)}function y(t){const n=[];return Array.isArray(t)?(t.forEach(e=>{e.polyline&&e.polyline.split(";").forEach(i=>{const[a,r]=i.split(",");a&&r&&n.push({longitude:parseFloat(a),latitude:parseFloat(r)})})}),n):(console.warn("骑行路线数据格式错误:",t),n)}function N(t,n="#0091ff",e=6){return[{points:t,color:n,width:e}]}function m(t){if(!t||isNaN(t))return"0米";const n=parseInt(t);return n>=1e3?(n/1e3).toFixed(1)+"公里":n+"米"}function w(t){if(!t||isNaN(t))return"0分钟";const n=Math.round(t/60);if(n>=60){const e=Math.floor(n/60),o=n%60;return o>0?`${e}小时${o}分钟`:`${e}小时`}return n+"分钟"}function M(t){return!t||isNaN(t)?"":"打车约"+parseInt(t)+"元"}function P(t){return Array.isArray(t)?t.map(n=>{const e=n.segments||[],o=[];return e.forEach((i,a)=>{if(i.bus&&i.bus.buslines&&i.bus.buslines[0]&&i.bus.buslines[0].name){let r=i.bus.buslines[0].name;a!==0&&(r="--"+r),o.push(r)}}),{...n,transport:o}}):(console.warn("公交路线数据格式错误:",t),[])}function b(t,n=null){const e=[];if(t){const{longitude:o,latitude:i}=s(t);e.push({latitude:i,longitude:o})}if(n){const{longitude:o,latitude:i}=s(n);e.push({latitude:i,longitude:o})}return e}function E(t){return t?typeof t.latitude!="number"||typeof t.longitude!="number"?(console.warn("标记点坐标不是数字类型:",t),!1):isNaN(t.latitude)||isNaN(t.longitude)?(console.warn("标记点坐标为NaN:",t),!1):t.latitude<-90||t.latitude>90||t.longitude<-180||t.longitude>180?(console.warn("标记点坐标超出有效范围:",t),!1):t.latitude===0&&t.longitude===0?(console.warn("标记点坐标为零:",t),!1):!0:(console.warn("标记点数据为空"),!1)}function F(t){return Array.isArray(t)?t.filter(E):(console.warn("标记点数据不是数组:",t),[])}function A(t,n){console.error(`${t}失败:`,n);const e=n.errMsg||n.message||"操作失败，请重试";d.index.showToast({title:e,icon:"none",duration:2e3})}exports.createEndMarker=g;exports.createIncludePoints=b;exports.createPolyline=N;exports.createStartMarker=p;exports.formatDistance=m;exports.formatDuration=w;exports.formatTaxiCost=M;exports.handleMapError=A;exports.parseLocation=s;exports.parseRidingPoints=y;exports.parseRoutePoints=h;exports.processTransitData=P;exports.safeFilterMarkers=F;
