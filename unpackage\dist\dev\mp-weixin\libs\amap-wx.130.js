"use strict";const c=require("../common/vendor.js");function d(e){this.key=e.key,this.requestConfig={key:e.key,s:"rsx",platform:"WXJS",appname:e.key,sdkversion:"1.2.0",logversion:"2.0"},this.MeRequestConfig={key:e.key,serviceName:"https://restapi.amap.com/rest/me"}}d.prototype.getWxLocation=function(e,t){c.wx$1.getLocation({type:"gcj02",success:function(s){s=s.longitude+","+s.latitude,c.wx$1.setStorage({key:"userLocation",data:s}),t(s)},fail:function(s){c.wx$1.getStorage({key:"userLocation",success:function(o){o.data&&t(o.data)}}),e.fail({errCode:"0",errMsg:s.errMsg||""})}})};d.prototype.getMEKeywordsSearch=function(e){if(!e.options)return e.fail({errCode:"0",errMsg:"缺少必要参数"});var t=e.options,s=this.MeRequestConfig,o={key:s.key,s:"rsx",platform:"WXJS",appname:e.key,sdkversion:"1.2.0",logversion:"2.0"};t.layerId&&(o.layerId=t.layerId),t.keywords&&(o.keywords=t.keywords),t.city&&(o.city=t.city),t.filter&&(o.filter=t.filter),t.sortrule&&(o.sortrule=t.sortrule),t.pageNum&&(o.pageNum=t.pageNum),t.pageSize&&(o.pageSize=t.pageSize),t.sig&&(o.sig=t.sig),c.wx$1.request({url:s.serviceName+"/cpoint/datasearch/local",data:o,method:"GET",header:{"content-type":"application/json"},success:function(i){(i=i.data)&&i.status&&i.status==="1"&&i.code===0?e.success(i.data):e.fail({errCode:"0",errMsg:i})},fail:function(i){e.fail({errCode:"0",errMsg:i.errMsg||""})}})};d.prototype.getMEIdSearch=function(e){if(!e.options)return e.fail({errCode:"0",errMsg:"缺少必要参数"});var t=e.options,s=this.MeRequestConfig,o={key:s.key,s:"rsx",platform:"WXJS",appname:e.key,sdkversion:"1.2.0",logversion:"2.0"};t.layerId&&(o.layerId=t.layerId),t.id&&(o.id=t.id),t.sig&&(o.sig=t.sig),c.wx$1.request({url:s.serviceName+"/cpoint/datasearch/id",data:o,method:"GET",header:{"content-type":"application/json"},success:function(i){(i=i.data)&&i.status&&i.status==="1"&&i.code===0?e.success(i.data):e.fail({errCode:"0",errMsg:i})},fail:function(i){e.fail({errCode:"0",errMsg:i.errMsg||""})}})};d.prototype.getMEPolygonSearch=function(e){if(!e.options)return e.fail({errCode:"0",errMsg:"缺少必要参数"});var t=e.options,s=this.MeRequestConfig,o={key:s.key,s:"rsx",platform:"WXJS",appname:e.key,sdkversion:"1.2.0",logversion:"2.0"};t.layerId&&(o.layerId=t.layerId),t.keywords&&(o.keywords=t.keywords),t.polygon&&(o.polygon=t.polygon),t.filter&&(o.filter=t.filter),t.sortrule&&(o.sortrule=t.sortrule),t.pageNum&&(o.pageNum=t.pageNum),t.pageSize&&(o.pageSize=t.pageSize),t.sig&&(o.sig=t.sig),c.wx$1.request({url:s.serviceName+"/cpoint/datasearch/polygon",data:o,method:"GET",header:{"content-type":"application/json"},success:function(i){(i=i.data)&&i.status&&i.status==="1"&&i.code===0?e.success(i.data):e.fail({errCode:"0",errMsg:i})},fail:function(i){e.fail({errCode:"0",errMsg:i.errMsg||""})}})};d.prototype.getMEaroundSearch=function(e){if(!e.options)return e.fail({errCode:"0",errMsg:"缺少必要参数"});var t=e.options,s=this.MeRequestConfig,o={key:s.key,s:"rsx",platform:"WXJS",appname:e.key,sdkversion:"1.2.0",logversion:"2.0"};t.layerId&&(o.layerId=t.layerId),t.keywords&&(o.keywords=t.keywords),t.center&&(o.center=t.center),t.radius&&(o.radius=t.radius),t.filter&&(o.filter=t.filter),t.sortrule&&(o.sortrule=t.sortrule),t.pageNum&&(o.pageNum=t.pageNum),t.pageSize&&(o.pageSize=t.pageSize),t.sig&&(o.sig=t.sig),c.wx$1.request({url:s.serviceName+"/cpoint/datasearch/around",data:o,method:"GET",header:{"content-type":"application/json"},success:function(i){(i=i.data)&&i.status&&i.status==="1"&&i.code===0?e.success(i.data):e.fail({errCode:"0",errMsg:i})},fail:function(i){e.fail({errCode:"0",errMsg:i.errMsg||""})}})};d.prototype.getGeo=function(e){var t=this.requestConfig,s=e.options;t={key:this.key,extensions:"all",s:t.s,platform:t.platform,appname:this.key,sdkversion:t.sdkversion,logversion:t.logversion},s.address&&(t.address=s.address),s.city&&(t.city=s.city),s.batch&&(t.batch=s.batch),s.sig&&(t.sig=s.sig),c.wx$1.request({url:"https://restapi.amap.com/v3/geocode/geo",data:t,method:"GET",header:{"content-type":"application/json"},success:function(o){(o=o.data)&&o.status&&o.status==="1"?e.success(o):e.fail({errCode:"0",errMsg:o})},fail:function(o){e.fail({errCode:"0",errMsg:o.errMsg||""})}})};d.prototype.getRegeo=function(e){function t(o){var i=s.requestConfig;c.wx$1.request({url:"https://restapi.amap.com/v3/geocode/regeo",data:{key:s.key,location:o,extensions:"all",s:i.s,platform:i.platform,appname:s.key,sdkversion:i.sdkversion,logversion:i.logversion},method:"GET",header:{"content-type":"application/json"},success:function(a){if(a.data.status&&a.data.status=="1"){a=a.data.regeocode;var n=a.addressComponent,r=[],u=a.roads[0].name+"附近",l=o.split(",")[0],g=o.split(",")[1];if(a.pois&&a.pois[0]){u=a.pois[0].name+"附近";var p=a.pois[0].location;p&&(l=parseFloat(p.split(",")[0]),g=parseFloat(p.split(",")[1]))}n.provice&&r.push(n.provice),n.city&&r.push(n.city),n.district&&r.push(n.district),n.streetNumber&&n.streetNumber.street&&n.streetNumber.number?(r.push(n.streetNumber.street),r.push(n.streetNumber.number)):r.push(a.roads[0].name),r=r.join(""),e.success([{iconPath:e.iconPath,width:e.iconWidth,height:e.iconHeight,name:r,desc:u,longitude:l,latitude:g,id:0,regeocodeData:a}])}else e.fail({errCode:a.data.infocode,errMsg:a.data.info})},fail:function(a){e.fail({errCode:"0",errMsg:a.errMsg||""})}})}var s=this;e.location?t(e.location):s.getWxLocation(e,function(o){t(o)})};d.prototype.getWeather=function(e){function t(a){var n="base";e.type&&e.type=="forecast"&&(n="all"),c.wx$1.request({url:"https://restapi.amap.com/v3/weather/weatherInfo",data:{key:o.key,city:a,extensions:n,s:i.s,platform:i.platform,appname:o.key,sdkversion:i.sdkversion,logversion:i.logversion},method:"GET",header:{"content-type":"application/json"},success:function(r){if(r.data.status&&r.data.status=="1")if(r.data.lives){if((r=r.data.lives)&&0<r.length){r=r[0];var u={city:{text:"城市",data:r.city},weather:{text:"天气",data:r.weather},temperature:{text:"温度",data:r.temperature},winddirection:{text:"风向",data:r.winddirection+"风"},windpower:{text:"风力",data:r.windpower+"级"},humidity:{text:"湿度",data:r.humidity+"%"}};u.liveData=r,e.success(u)}}else r.data.forecasts&&r.data.forecasts[0]&&e.success({forecast:r.data.forecasts[0]});else e.fail({errCode:r.data.infocode,errMsg:r.data.info})},fail:function(r){e.fail({errCode:"0",errMsg:r.errMsg||""})}})}function s(a){c.wx$1.request({url:"https://restapi.amap.com/v3/geocode/regeo",data:{key:o.key,location:a,extensions:"all",s:i.s,platform:i.platform,appname:o.key,sdkversion:i.sdkversion,logversion:i.logversion},method:"GET",header:{"content-type":"application/json"},success:function(n){if(n.data.status&&n.data.status=="1"){if(n=n.data.regeocode,n.addressComponent)var r=n.addressComponent.adcode;else n.aois&&0<n.aois.length&&(r=n.aois[0].adcode);t(r)}else e.fail({errCode:n.data.infocode,errMsg:n.data.info})},fail:function(n){e.fail({errCode:"0",errMsg:n.errMsg||""})}})}var o=this,i=o.requestConfig;e.city?t(e.city):o.getWxLocation(e,function(a){s(a)})};d.prototype.getPoiAround=function(e){function t(i){i={key:s.key,location:i,s:o.s,platform:o.platform,appname:s.key,sdkversion:o.sdkversion,logversion:o.logversion},e.querytypes&&(i.types=e.querytypes),e.querykeywords&&(i.keywords=e.querykeywords),c.wx$1.request({url:"https://restapi.amap.com/v3/place/around",data:i,method:"GET",header:{"content-type":"application/json"},success:function(a){if(a.data.status&&a.data.status=="1"){if((a=a.data)&&a.pois){for(var n=[],r=0;r<a.pois.length;r++){var u=r==0?e.iconPathSelected:e.iconPath;n.push({latitude:parseFloat(a.pois[r].location.split(",")[1]),longitude:parseFloat(a.pois[r].location.split(",")[0]),iconPath:u,width:22,height:32,id:r,name:a.pois[r].name,address:a.pois[r].address})}e.success({markers:n,poisData:a.pois})}}else e.fail({errCode:a.data.infocode,errMsg:a.data.info})},fail:function(a){e.fail({errCode:"0",errMsg:a.errMsg||""})}})}var s=this,o=s.requestConfig;e.location?t(e.location):s.getWxLocation(e,function(i){t(i)})};d.prototype.getStaticmap=function(e){function t(i){s.push("location="+i),e.zoom&&s.push("zoom="+e.zoom),e.size&&s.push("size="+e.size),e.scale&&s.push("scale="+e.scale),e.markers&&s.push("markers="+e.markers),e.labels&&s.push("labels="+e.labels),e.paths&&s.push("paths="+e.paths),e.traffic&&s.push("traffic="+e.traffic),i="https://restapi.amap.com/v3/staticmap?"+s.join("&"),e.success({url:i})}var s=[];s.push("key="+this.key);var o=this.requestConfig;s.push("s="+o.s),s.push("platform="+o.platform),s.push("appname="+o.appname),s.push("sdkversion="+o.sdkversion),s.push("logversion="+o.logversion),e.location?t(e.location):this.getWxLocation(e,function(i){t(i)})};d.prototype.getInputtips=function(e){var t=Object.assign({},this.requestConfig);e.location&&(t.location=e.location),e.keywords&&(t.keywords=e.keywords),e.type&&(t.type=e.type),e.city&&(t.city=e.city),e.citylimit&&(t.citylimit=e.citylimit),c.wx$1.request({url:"https://restapi.amap.com/v3/assistant/inputtips",data:t,method:"GET",header:{"content-type":"application/json"},success:function(s){s&&s.data&&s.data.tips&&e.success({tips:s.data.tips})},fail:function(s){e.fail({errCode:"0",errMsg:s.errMsg||""})}})};d.prototype.getDrivingRoute=function(e){var t=Object.assign({},this.requestConfig);e.origin&&(t.origin=e.origin),e.destination&&(t.destination=e.destination),e.strategy&&(t.strategy=e.strategy),e.waypoints&&(t.waypoints=e.waypoints),e.avoidpolygons&&(t.avoidpolygons=e.avoidpolygons),e.avoidroad&&(t.avoidroad=e.avoidroad),c.wx$1.request({url:"https://restapi.amap.com/v3/direction/driving",data:t,method:"GET",header:{"content-type":"application/json"},success:function(s){s&&s.data&&s.data.route&&e.success({paths:s.data.route.paths,taxi_cost:s.data.route.taxi_cost||""})},fail:function(s){e.fail({errCode:"0",errMsg:s.errMsg||""})}})};d.prototype.getWalkingRoute=function(e){var t=Object.assign({},this.requestConfig);e.origin&&(t.origin=e.origin),e.destination&&(t.destination=e.destination),c.wx$1.request({url:"https://restapi.amap.com/v3/direction/walking",data:t,method:"GET",header:{"content-type":"application/json"},success:function(s){s&&s.data&&s.data.route&&e.success({paths:s.data.route.paths})},fail:function(s){e.fail({errCode:"0",errMsg:s.errMsg||""})}})};d.prototype.getTransitRoute=function(e){var t=Object.assign({},this.requestConfig);e.origin&&(t.origin=e.origin),e.destination&&(t.destination=e.destination),e.strategy&&(t.strategy=e.strategy),e.city&&(t.city=e.city),e.cityd&&(t.cityd=e.cityd),c.wx$1.request({url:"https://restapi.amap.com/v3/direction/transit/integrated",data:t,method:"GET",header:{"content-type":"application/json"},success:function(s){s&&s.data&&s.data.route&&(s=s.data.route,e.success({distance:s.distance||"",taxi_cost:s.taxi_cost||"",transits:s.transits}))},fail:function(s){e.fail({errCode:"0",errMsg:s.errMsg||""})}})};d.prototype.getRidingRoute=function(e){var t=Object.assign({},this.requestConfig);e.origin&&(t.origin=e.origin),e.destination&&(t.destination=e.destination),c.wx$1.request({url:"https://restapi.amap.com/v3/direction/riding",data:t,method:"GET",header:{"content-type":"application/json"},success:function(s){s&&s.data&&s.data.route&&e.success({paths:s.data.route.paths})},fail:function(s){e.fail({errCode:"0",errMsg:s.errMsg||""})}})};exports.AMapWX=d;
