/**
 * 路线服务组合式函数
 * 处理各种类型的路线规划（驾车、步行、公交、骑行）
 */

import AMapWX from '../libs/amap-wx.130.js'
import gaode_key from '../libs/config.js'
import { 
  parseRoutePoints, 
  parseRidingPoints, 
  createPolyline,
  formatDistance,
  formatDuration,
  formatTaxiCost,
  processTransitData,
  handleMapError
} from '../utils/mapUtils.js'

export function useRouteService() {
  
  /**
   * 创建高德地图实例
   * @returns {object} 高德地图实例
   */
  function createAmapInstance() {
    return new AMapWX({
      key: gaode_key.config.key
    })
  }
  
  /**
   * 驾车路线规划
   * @param {object} params - 路线参数
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getDrivingRoute(params, onSuccess, onFail) {
    const { origin, destination } = params
    
    if (!origin || !destination) {
      console.warn('驾车路线规划缺少必要参数')
      return
    }
    
    const myAmapFun = createAmapInstance()
    
    myAmapFun.getDrivingRoute({
      origin,
      destination,
      success: function(data) {
        console.log('驾车路线数据:', data)
        
        try {
          // 解析路线点
          const points = parseRoutePoints(data.paths?.[0]?.steps || [])
          
          // 创建路线样式
          const polylineData = createPolyline(points)
          
          // 格式化距离和费用信息
          const routeInfo = {
            distance: data.paths?.[0]?.distance ? formatDistance(data.paths[0].distance) : '',
            cost: data.taxi_cost ? formatTaxiCost(data.taxi_cost) : '',
            polyline: polylineData
          }
          
          onSuccess && onSuccess(routeInfo)
          
        } catch (error) {
          console.error('驾车路线数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('驾车路线规划失败:', error)
        handleMapError('驾车路线规划', error)
        onFail && onFail(error)
      }
    })
  }
  
  /**
   * 步行路线规划
   * @param {object} params - 路线参数
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getWalkingRoute(params, onSuccess, onFail) {
    const { origin, destination } = params
    
    if (!origin || !destination) {
      console.warn('步行路线规划缺少必要参数')
      return
    }
    
    const myAmapFun = createAmapInstance()
    
    myAmapFun.getWalkingRoute({
      origin,
      destination,
      success: function(data) {
        console.log('步行路线数据:', data)
        
        try {
          // 解析路线点
          const points = parseRoutePoints(data.paths?.[0]?.steps || [])
          
          // 创建路线样式
          const polylineData = createPolyline(points)
          
          // 格式化距离和时间信息
          const routeInfo = {
            distance: data.paths?.[0]?.distance ? formatDistance(data.paths[0].distance) : '',
            cost: data.paths?.[0]?.duration ? formatDuration(data.paths[0].duration) : '',
            polyline: polylineData
          }
          
          onSuccess && onSuccess(routeInfo)
          
        } catch (error) {
          console.error('步行路线数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('步行路线规划失败:', error)
        handleMapError('步行路线规划', error)
        onFail && onFail(error)
      }
    })
  }
  
  /**
   * 公交路线规划
   * @param {object} params - 路线参数
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getTransitRoute(params, onSuccess, onFail) {
    const { origin, destination, city = '广州' } = params
    
    if (!origin || !destination) {
      console.warn('公交路线规划缺少必要参数')
      return
    }
    
    const myAmapFun = createAmapInstance()
    
    myAmapFun.getTransitRoute({
      origin,
      destination,
      city,
      success: function(data) {
        console.log('公交路线数据:', data)
        
        try {
          if (data && data.transits) {
            // 处理公交路线数据
            const processedTransits = processTransitData(data.transits)
            
            onSuccess && onSuccess({
              transits: processedTransits
            })
          } else {
            console.warn('公交路线数据为空')
            onFail && onFail({ message: '未找到公交路线' })
          }
          
        } catch (error) {
          console.error('公交路线数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('公交路线规划失败:', error)
        handleMapError('公交路线规划', error)
        onFail && onFail(error)
      }
    })
  }
  
  /**
   * 骑行路线规划
   * @param {object} params - 路线参数
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getRidingRoute(params, onSuccess, onFail) {
    const { origin, destination } = params
    
    if (!origin || !destination) {
      console.warn('骑行路线规划缺少必要参数')
      return
    }
    
    const myAmapFun = createAmapInstance()
    
    myAmapFun.getRidingRoute({
      origin,
      destination,
      success: function(data) {
        console.log('骑行路线数据:', data)
        
        try {
          if (data && data.paths && data.paths[0]) {
            const path = data.paths[0]
            
            // 解析骑行路线点
            const points = parseRidingPoints(path.rides || [])
            
            // 创建路线样式
            const polylineData = createPolyline(points)
            
            // 格式化距离和时间信息
            const routeInfo = {
              distance: path.distance ? formatDistance(path.distance) : '',
              cost: path.duration ? formatDuration(path.duration) : '',
              polyline: polylineData
            }
            
            onSuccess && onSuccess(routeInfo)
            
          } else {
            console.warn('骑行路线数据为空')
            onFail && onFail({ message: '未找到骑行路线' })
          }
          
        } catch (error) {
          console.error('骑行路线数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('骑行路线规划失败:', error)
        handleMapError('骑行路线规划', error)
        onFail && onFail(error)
      }
    })
  }
  
  /**
   * 统一的路线规划接口
   * @param {string} routeType - 路线类型：car, walk, bus, riding
   * @param {object} params - 路线参数
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function planRoute(routeType, params, onSuccess, onFail) {
    console.log(`开始${routeType}路线规划:`, params)
    
    switch (routeType) {
      case 'car':
        getDrivingRoute(params, onSuccess, onFail)
        break
      case 'walk':
        getWalkingRoute(params, onSuccess, onFail)
        break
      case 'bus':
        getTransitRoute(params, onSuccess, onFail)
        break
      case 'riding':
        getRidingRoute(params, onSuccess, onFail)
        break
      default:
        console.warn('不支持的路线类型:', routeType)
        onFail && onFail({ message: '不支持的路线类型' })
    }
  }
  
  // 返回方法
  return {
    getDrivingRoute,
    getWalkingRoute,
    getTransitRoute,
    getRidingRoute,
    planRoute
  }
}
