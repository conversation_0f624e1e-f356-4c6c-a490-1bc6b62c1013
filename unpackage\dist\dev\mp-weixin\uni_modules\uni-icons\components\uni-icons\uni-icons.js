"use strict";const s=require("./uniicons_file_vue.js"),t=require("../../../../common/vendor.js"),r=e=>typeof e=="number"||/^[0-9]*$/g.test(e)?e+"px":e,u={name:"UniIcons",emits:["click"],props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customPrefix:{type:String,default:""},fontFamily:{type:String,default:""}},data(){return{icons:s.fontData}},computed:{unicode(){let e=this.icons.find(i=>i.font_class===this.type);return e?e.unicode:""},iconSize(){return r(this.size)},styleObj(){return this.fontFamily!==""?`color: ${this.color}; font-size: ${this.iconSize}; font-family: ${this.fontFamily};`:`color: ${this.color}; font-size: ${this.iconSize};`}},methods:{_onClick(){this.$emit("click")}}};function f(e,i,n,m,a,o){return{a:t.s(o.styleObj),b:t.n("uniui-"+n.type),c:t.n(n.customPrefix),d:t.n(n.customPrefix?n.type:""),e:t.o((...c)=>o._onClick&&o._onClick(...c))}}const l=t._export_sfc(u,[["render",f],["__file","D:/zuomian/前端学习/uniapp项目/GaodeMap/uni_modules/uni-icons/components/uni-icons/uni-icons.vue"]]);wx.createComponent(l);
