/**
 * 位置服务组合式函数
 * 处理地点搜索、POI查询等功能
 */

import AMapWX from '../libs/amap-wx.130.js'
import gaode_key from '../libs/config.js'
import { handleMapError, safeFilterMarkers } from '../utils/mapUtils.js'

export function useLocationService() {

  // 高德地图实例（懒加载单例）
  let amapInstance = null

  function getAmapInstance() {
    if (!amapInstance) {
      amapInstance = new AMapWX({
        key: gaode_key.config.key
      })
    }
    return amapInstance
  }
  
  /**
   * 获取当前位置周边POI信息
   * @param {object} options - 搜索选项
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getPoiAround(options = {}, onSuccess, onFail) {
    const {
      keywords = '', // 搜索关键词
      iconPath = '/static/marker.png', // 默认标记图标
      iconPathSelected = '/static/marker_checked.png' // 选中标记图标
    } = options

    console.log('开始搜索周边POI:', keywords || '当前位置')

    const myAmapFun = getAmapInstance()

    const searchParams = {
      iconPath,
      iconPathSelected,
      success: function(data) {
        console.log('POI搜索结果:', data)

        try {
          if (data && data.markers && data.markers.length > 0) {
            // 🔥 关键修复：使用安全过滤函数过滤掉无效的标记点，防止访问undefined的lat属性
            const validMarkers = safeFilterMarkers(data.markers)

            if (validMarkers.length === 0) {
              console.warn('没有有效的POI标记点')
              onFail && onFail({ message: '未找到有效的地点信息' })
              return
            }

            // 获取第一个有效的POI标记点
            const firstMarker = validMarkers[0]

            // 构造第一个POI对象，确保包含location字段
            const firstPoi = {
              id: firstMarker.id,
              name: firstMarker.name || '未知地点',
              address: firstMarker.address || '',
              latitude: firstMarker.latitude,
              longitude: firstMarker.longitude,
              // 重要：构造location字符串，格式为"经度,纬度"
              location: `${firstMarker.longitude},${firstMarker.latitude}`,
              iconPath: firstMarker.iconPath,
              width: firstMarker.width,
              height: firstMarker.height
            }

            // 处理搜索结果，使用过滤后的有效标记点
            const processedData = {
              markers: validMarkers,        // 地图显示用的标记点（视觉层面）
              currentLocation: firstPoi,    // 第一个POI作为当前选中位置
              poisData: data.poisData || [] // 详细的POI业务信息（数据层面）如：类型、电话、营业时间、评分
            }

            console.log('处理后的位置数据:', processedData)
            onSuccess && onSuccess(processedData)
          } else {
            console.warn('POI搜索无结果')
            onFail && onFail({ message: '未找到相关地点' })
          }

        } catch (error) {
          console.error('POI数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('POI搜索失败:', error)
        handleMapError('地点搜索', error)
        onFail && onFail(error)
      }
    }
    
    // 如果有关键词，添加到搜索参数中
    if (keywords) {
      searchParams.querykeywords = keywords
    }
    
    myAmapFun.getPoiAround(searchParams)
  }
  
  /**
   * 获取输入提示
   * @param {object} options - 搜索选项
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getInputTips(options, onSuccess, onFail) {
    const {
      keywords = '', // 搜索关键词
      location = '', // 当前位置
      city = '' // 城市
    } = options
    
    if (!keywords.trim()) {
      // 没有输入关键词，则提示为空数组
      onSuccess && onSuccess({ tips: [] })
      return
    }
    
    const myAmapFun = getAmapInstance()
    
    myAmapFun.getInputtips({
      keywords: keywords.trim(),
      location,
      city,
      success: function(data) {
        console.log('输入提示结果:', data)
        
        try {
          if (data && data.tips) {
            onSuccess && onSuccess({
              tips: data.tips,
              keyword: keywords
            })
          } else {
            onSuccess && onSuccess({ tips: [] })
          }
          
        } catch (error) {
          console.error('输入提示数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('获取输入提示失败:', error)
        handleMapError('获取搜索提示', error)
        onFail && onFail(error)
      }
    })
  }
  
  /**
   * 地理编码 - 将地址转换为坐标
   * @param {string} address - 地址字符串
   * @param {string} city - 城市名称
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getGeocode(address, city = '', onSuccess, onFail) {
    if (!address || !address.trim()) {
      console.warn('地理编码缺少地址参数')
      onFail && onFail({ message: '地址不能为空' })
      return
    }
    
    console.log('地理编码:', address)

    const myAmapFun = getAmapInstance()
    
    myAmapFun.getGeocode({
      address: address.trim(),
      city,
      success: function(data) {
        console.log('地理编码结果:', data)
        
        try {
          if (data && data.geocodes && data.geocodes.length > 0) {
            const geocode = data.geocodes[0]
            
            const locationInfo = {
              name: address,                               // 用户输入的原始地址
              location: geocode.location,                  // "经度,纬度"
              district: geocode.district || city,          // 所属行政区划（如："朝阳区"、"浦东新区"）
              adcode: geocode.adcode,                      // 地区编码（如："110105" 代表北京市朝阳区）
              formatted_address: geocode.formatted_address // 高德标准化后的完整地址（如："北京市朝阳区建国门外大街1号"）
            }
            
            onSuccess && onSuccess(locationInfo)
          } else {
            console.warn('地理编码无结果')
            onFail && onFail({ message: '未找到该地址' })
          }
          
        } catch (error) {
          console.error('地理编码数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('地理编码失败:', error)
        handleMapError('地址解析', error)
        onFail && onFail(error)
      }
    })
  }
  
  /**
   * 逆地理编码 - 将坐标转换为地址
   * @param {string} location - 坐标字符串 "经度,纬度"
   * @param {function} onSuccess - 成功回调
   * @param {function} onFail - 失败回调
   */
  function getRegeocode(location, onSuccess, onFail) {
    if (!location || !location.includes(',')) {
      console.warn('逆地理编码缺少有效坐标')
      onFail && onFail({ message: '坐标格式错误' })
      return
    }

    const myAmapFun = getAmapInstance()
    
    myAmapFun.getRegeocode({
      location,
      success: function(data) {
        console.log('逆地理编码结果:', data)
        
        try {
          if (data && data.regeocodes && data.regeocodes.length > 0) {
            const regeocode = data.regeocodes[0]
            
            const addressInfo = {
              formatted_address: regeocode.formatted_address,
              province: regeocode.addressComponent?.province || '',
              city: regeocode.addressComponent?.city || '',
              district: regeocode.addressComponent?.district || '',
              street: regeocode.addressComponent?.street || '',
              streetNumber: regeocode.addressComponent?.streetNumber || '',
              location: location
            }
            
            onSuccess && onSuccess(addressInfo)
          } else {
            console.warn('逆地理编码无结果')
            onFail && onFail({ message: '无法解析该坐标' })
          }
          
        } catch (error) {
          console.error('逆地理编码数据处理失败:', error)
          onFail && onFail(error)
        }
      },
      fail: function(error) {
        console.error('逆地理编码失败:', error)
        handleMapError('坐标解析', error)
        onFail && onFail(error)
      }
    })
  }
  

  
  // 返回方法
  return {
    getPoiAround,
    getInputTips,
    getGeocode,
    getRegeocode
  }
}
