"use strict";const t=require("../common/vendor.js"),n=require("../utils/mapUtils.js");function R(){const p=t.ref(!0),c=t.ref([]),k=t.ref([]),s=t.ref([]),i=t.ref(""),v=t.ref(""),M=t.ref(""),u=t.ref(""),l=t.ref(""),d=t.ref(""),o=t.ref({}),h=t.ref({}),y=t.ref(""),_=t.ref(""),w=t.ref(!1),L=t.ref("car"),S=t.ref([]),f=t.computed(()=>!v.value||!i.value?"":`${v.value},${i.value}`),g=t.computed(()=>!l.value||!u.value?"":`${l.value},${u.value}`),$=t.computed(()=>f.value&&g.value);function P(e){try{if(!e){console.warn("设置当前位置失败：位置信息为空");return}let r=e.location;if(!r)if(e.longitude&&e.latitude)r=`${e.longitude},${e.latitude}`,console.log("从经纬度构造位置字符串:",r);else{console.warn("设置当前位置失败：缺少位置坐标",e);return}const{longitude:a,latitude:m}=n.parseLocation(r);if(a===0&&m===0){console.warn("设置当前位置失败：位置坐标解析无效",r);return}v.value=a,i.value=m,M.value=e.name||"",console.log("⚠️ setCurrentLocation函数被调用，但不再直接设置markers以避免覆盖POI数据"),console.log("📍 仅设置地图中心位置:",e.name||"未知位置"),s.value=n.createIncludePoints(r),console.log("当前位置设置成功:",e.name||"未知位置")}catch(r){console.error("设置当前位置时发生错误:",r),n.handleMapError("设置当前位置",r)}}function A(e){try{if(!e){console.warn("设置目的地失败：位置信息为空");return}if(!e.location){console.warn("设置目的地失败：缺少位置坐标",e);return}const{longitude:r,latitude:a}=n.parseLocation(e.location);if(r===0&&a===0){console.warn("设置目的地失败：位置坐标解析无效",e.location);return}l.value=r,u.value=a,d.value=e.name||"",o.value=e,w.value=!0,console.log("目的地设置成功:",e.name||"未知目的地")}catch(r){console.error("设置目的地时发生错误:",r),n.handleMapError("设置目的地",r)}}function b(){if(!$.value){console.warn("缺少有效的起点或终点信息");return}try{const e=n.createStartMarker({location:f.value,name:M.value||"起点",district:"当前位置"}),r=n.createEndMarker({location:g.value,name:o.value.name||d.value||"终点",district:o.value.district||"目的地"});c.value=[e,r],s.value=n.createIncludePoints(f.value,g.value),E([e,r],1)}catch(e){n.handleMapError("设置路线标记",e)}}function E(e,r){if(!Array.isArray(e)||!e[r]){console.warn("标记点数据无效:",e,r);return}const a=e[r];h.value={name:a.name||"未知位置",desc:a.address||"暂无地址信息"}}function C(e,r){if(!Array.isArray(e)){console.warn("标记点数据格式错误:",e);return}const a=e.map((m,O)=>({...m,iconPath:O===r?"/static/marker_checked.png":"/static/marker.png"}));c.value=a}function T(){p.value=!0,c.value=[],k.value=[],s.value=[],h.value={},y.value="",_.value="",w.value=!1,u.value="",l.value="",d.value="",o.value={},S.value=[],console.log("地图状态已重置")}function q(e){if(!["car","walk","bus","riding"].includes(e)){console.warn("无效的导航类型:",e);return}L.value=e,p.value=e!=="bus",console.log("导航类型已切换为:",e)}return{mapState:p,markers:c,polyline:k,includePoints:s,latitude:i,longitude:v,city:M,latitude_e:u,longitude_e:l,city_e:d,mapEndObj:o,textData:h,distance:y,cost:_,daohang:w,gaode_type:L,transits:S,currentLocation:f,destinationLocation:g,hasValidRoute:$,setCurrentLocation:P,setDestination:A,setRouteMarkers:b,showMarkerInfo:E,changeMarkerColor:C,resetMapState:T,changeNavigationType:q}}exports.useMapState=R;
