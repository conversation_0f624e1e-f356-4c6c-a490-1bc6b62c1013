"use strict";const a=require("../../common/vendor.js"),b={__name:"Header",props:{type:{type:String,default:"car",validator:i=>["car","walk","bus","riding"].includes(i)},NavigationOrNot:{type:Boolean,default:!1},buttonsDisabled:{type:Boolean,default:!1}},emits:["changeType"],setup(i,{emit:c}){const t=i,n=a.ref("car"),d=[{key:"car",label:"驾车",icon:"🚗",description:"适合开车出行"},{key:"walk",label:"步行",icon:"🚶",description:"适合短距离步行"},{key:"bus",label:"公交",icon:"🚌",description:"使用公共交通"},{key:"riding",label:"骑行",icon:"🚴",description:"适合骑自行车"}];function s(){console.log("导航头部组件初始化:",t),t.type&&t.type!==n.value&&(n.value=t.type)}function o(e){if(t.buttonsDisabled){console.log("按钮已禁用，请先设置起点和终点"),a.index.showToast({title:"请先设置起点和终点",icon:"none",duration:2e3});return}if(!d.find(l=>l.key===e)){console.warn("无效的导航类型:",e);return}console.log(`切换导航类型: ${e}`),n.value=e,c("changeType",{gaode_type:e})}function u(){o("car")}function r(){o("walk")}function v(){o("bus")}function g(){o("riding")}return a.watch(()=>t.type,e=>{e&&e!==n.value&&(console.log("父组件导航类型变化:",e),n.value=e)}),a.watch(()=>t.NavigationOrNot,e=>{console.log("导航状态变化:",e?"开始导航":"停止导航")}),a.onMounted(()=>{s()}),(e,l)=>({a:a.n({"item-active":n.value==="car"&&i.NavigationOrNot,"item-disabled":i.buttonsDisabled}),b:a.o(u),c:a.n({"item-active":n.value==="walk"&&i.NavigationOrNot,"item-disabled":i.buttonsDisabled}),d:a.o(r),e:a.n({"item-active":n.value==="bus"&&i.NavigationOrNot,"item-disabled":i.buttonsDisabled}),f:a.o(v),g:a.n({"item-active":n.value==="riding"&&i.NavigationOrNot,"item-disabled":i.buttonsDisabled}),h:a.o(g),i:a.n({"navigation-active":i.NavigationOrNot})})}},f=a._export_sfc(b,[["__scopeId","data-v-5ada1ca4"],["__file","D:/zuomian/前端学习/uniapp项目/GaodeMap/components/Header/Header.vue"]]);wx.createComponent(f);
