/**
 * 地图状态管理组合式函数
 * 管理地图的基本状态和标记点
 */

import { ref, computed } from 'vue'
import { 
  parseLocation, 
  createMarker, 
  createStartMarker, 
  createEndMarker,
  createIncludePoints,
  handleMapError
} from '../utils/mapUtils.js'

export function useMapState() {
  // ===== 响应式状态 =====
  
  // 地图基本状态
  const mapState = ref(true) // 地图显示状态
  const markers = ref([]) // 地图标记点
  const polyline = ref([]) // 路线数据
  const includePoints = ref([]) // 地图包含的点
  
  // 位置信息
  const latitude = ref('') // 当前纬度
  const longitude = ref('') // 当前经度
  const city = ref('') // 当前城市
  
  // 目的地信息
  const latitude_e = ref('') // 目的地纬度
  const longitude_e = ref('') // 目的地经度
  const city_e = ref('') // 目的地城市
  const mapEndObj = ref({}) // 目的地完整信息
  
  // 显示信息
  const textData = ref({}) // 当前显示的文本信息
  const distance = ref('') // 距离信息
  const cost = ref('') // 费用/时间信息
  const daohang = ref(false) // 是否处于导航状态
  
  // 导航类型
  const gaode_type = ref('car') // 当前导航类型：car, walk, bus, riding
  
  // 公交信息
  const transits = ref([]) // 公交路线信息
  
  // ===== 计算属性 =====
  
  // 当前位置坐标字符串
  const currentLocation = computed(() => {
    if (!longitude.value || !latitude.value) return ''
    return `${longitude.value},${latitude.value}`
  })
  
  // 目的地坐标字符串
  const destinationLocation = computed(() => {
    if (!longitude_e.value || !latitude_e.value) return ''
    return `${longitude_e.value},${latitude_e.value}`
  })
  
  // 是否有有效的起点和终点
  const hasValidRoute = computed(() => {
    return currentLocation.value && destinationLocation.value
  })
  
  // ===== 方法 =====
  
  /**
   * 设置当前位置
   * @param {object} locationInfo - 位置信息对象
   */
  function setCurrentLocation(locationInfo) {
    try {
      // 验证输入参数
      if (!locationInfo) {
        console.warn('设置当前位置失败：位置信息为空')
        return
      }

      // 检查是否有有效的位置坐标
      // 支持多种位置数据格式
      let locationString = locationInfo.location

      if (!locationString) {
        // 如果没有location字段，尝试从经纬度构造
        if (locationInfo.longitude && locationInfo.latitude) {
          locationString = `${locationInfo.longitude},${locationInfo.latitude}`
          console.log('从经纬度构造位置字符串:', locationString)
        } else {
          console.warn('设置当前位置失败：缺少位置坐标', locationInfo)
          return
        }
      }

      const { longitude: lng, latitude: lat } = parseLocation(locationString)

      // 检查解析结果是否有效
      if (lng === 0 && lat === 0) {
        console.warn('设置当前位置失败：位置坐标解析无效', locationString)
        return
      }

      longitude.value = lng
      latitude.value = lat
      city.value = locationInfo.name || ''

      // ⚠️ 注意：这个函数现在只设置地图中心，不再直接操作markers
      // POI标记点的管理已移到页面级别的initializeCurrentLocation函数中
      console.log('⚠️ setCurrentLocation函数被调用，但不再直接设置markers以避免覆盖POI数据')
      console.log('📍 仅设置地图中心位置:', locationInfo.name || '未知位置')

      // 只设置包含点，不操作markers
      includePoints.value = createIncludePoints(locationString)

      console.log('当前位置设置成功:', locationInfo.name || '未知位置')
    } catch (error) {
      console.error('设置当前位置时发生错误:', error)
      handleMapError('设置当前位置', error)
    }
  }
  
  /**
   * 设置目的地位置
   * @param {object} locationInfo - 目的地信息对象
   */
  function setDestination(locationInfo) {
    try {
      // 验证输入参数
      if (!locationInfo) {
        console.warn('设置目的地失败：位置信息为空')
        return
      }

      // 检查是否有有效的位置坐标
      if (!locationInfo.location) {
        console.warn('设置目的地失败：缺少位置坐标', locationInfo)
        return
      }

      const { longitude: lng, latitude: lat } = parseLocation(locationInfo.location)

      // 检查解析结果是否有效
      if (lng === 0 && lat === 0) {
        console.warn('设置目的地失败：位置坐标解析无效', locationInfo.location)
        return
      }

      longitude_e.value = lng
      latitude_e.value = lat
      city_e.value = locationInfo.name || ''
      mapEndObj.value = locationInfo

      daohang.value = true

      console.log('目的地设置成功:', locationInfo.name || '未知目的地')
    } catch (error) {
      console.error('设置目的地时发生错误:', error)
      handleMapError('设置目的地', error)
    }
  }
  
  /**
   * 设置路线标记点（起点和终点）
   */
  function setRouteMarkers() {
    if (!hasValidRoute.value) {
      console.warn('缺少有效的起点或终点信息')
      return
    }
    
    try {
      const startMarker = createStartMarker({
        location: currentLocation.value,
        name: city.value || '起点',
        district: '当前位置'
      })
      
      const endMarker = createEndMarker({
        location: destinationLocation.value,
        name: mapEndObj.value.name || city_e.value || '终点',
        district: mapEndObj.value.district || '目的地'
      })
      
      markers.value = [startMarker, endMarker]
      includePoints.value = createIncludePoints(
        currentLocation.value, 
        destinationLocation.value
      )
      
      // 显示终点信息
      showMarkerInfo([startMarker, endMarker], 1)
      
    } catch (error) {
      handleMapError('设置路线标记', error)
    }
  }
  
  /**
   * 显示标记点信息
   * @param {array} markersData - 标记点数据数组
   * @param {number} index - 要显示的标记点索引
   */
  function showMarkerInfo(markersData, index) {
    if (!Array.isArray(markersData) || !markersData[index]) {
      console.warn('标记点数据无效:', markersData, index)
      return
    }
    
    const marker = markersData[index]
    textData.value = {
      name: marker.name || '未知位置',
      desc: marker.address || '暂无地址信息'
    }
  }
  
  /**
   * 改变标记点颜色（选中状态）
   * @param {array} markersData - 标记点数据数组
   * @param {number} selectedIndex - 选中的标记点索引
   */
  function changeMarkerColor(markersData, selectedIndex) {
    if (!Array.isArray(markersData)) {
      console.warn('标记点数据格式错误:', markersData)
      return
    }
    
    const updatedMarkers = markersData.map((marker, index) => ({
      ...marker,
      iconPath: index === selectedIndex 
        ? '/static/marker_checked.png' 
        : '/static/marker.png'
    }))
    
    markers.value = updatedMarkers
  }
  
  /**
   * 重置地图状态
   */
  function resetMapState() {
    mapState.value = true
    markers.value = []
    polyline.value = []
    includePoints.value = []
    textData.value = {}
    distance.value = ''
    cost.value = ''
    daohang.value = false
    
    // 清除目的地信息
    latitude_e.value = ''
    longitude_e.value = ''
    city_e.value = ''
    mapEndObj.value = {}
    
    // 清除公交信息
    transits.value = []
    
    console.log('地图状态已重置')
  }
  
  /**
   * 切换导航类型
   * @param {string} type - 导航类型
   */
  function changeNavigationType(type) {
    const validTypes = ['car', 'walk', 'bus', 'riding']
    if (!validTypes.includes(type)) {
      console.warn('无效的导航类型:', type)
      return
    }
    
    gaode_type.value = type
    mapState.value = type !== 'bus' // 公交模式不显示地图
    
    console.log('导航类型已切换为:', type)
  }
  
  // ===== 返回状态和方法 =====
  return {
    // 状态
    mapState,
    markers,
    polyline,
    includePoints,
    latitude,
    longitude,
    city,
    latitude_e,
    longitude_e,
    city_e,
    mapEndObj,
    textData,
    distance,
    cost,
    daohang,
    gaode_type,
    transits,
    
    // 计算属性
    currentLocation,
    destinationLocation,
    hasValidRoute,
    
    // 方法
    setCurrentLocation,
    setDestination,
    setRouteMarkers,
    showMarkerInfo,
    changeMarkerColor,
    resetMapState,
    changeNavigationType
  }
}
