<template>
  <!-- 地点搜索输入组件 -->
  <view class="search-input-container">
    <!-- 搜索输入框 - 使用uni-easyinput组件 -->
    <view class="input-wrapper">
      <uni-easyinput
        class="search-input"
        v-model="searchKeyword"
        :placeholder="placeholder"
        :clearable="true"
        :focus="isFocused"
        type="text"
        trim="both"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @clear="clearInput"
      >
        <!-- 自定义前缀图标 -->
        <template #left>
          <view class="input-icon">
            <text class="icon-location">📍</text>
          </view>
        </template>
      </uni-easyinput>
    </view>

    <!-- 搜索建议列表 -->
    <view v-if="showTips && tips.length > 0" class="tips-container">
      <view
        v-for="(tip, index) in tips"
        :key="tip.id || tip.name || index"
        class="tip-item"
        :data-info="tip"
        :data-keywords="tip.name"
        @touchstart="handleTipSelect"
      >
        <view class="tip-content">
          <text class="tip-name">{{ tip.name }}</text>
          <text v-if="tip.district" class="tip-address">{{ tip.district }}</text>
        </view>
        <view class="tip-arrow">→</view>
      </view>
    </view>

    <!-- 无结果提示 -->
    <view v-if="showTips && tips.length === 0 && searchKeyword" class="no-results">
      <text class="no-results-text">未找到相关地点</text>
    </view>
  </view>
</template>

<script setup>
/**
 * 地点搜索输入组件
 * 提供地点搜索、输入提示、位置选择等功能
 * 使用 Vue 3 组合式 API 和高德地图 API
 */

import { ref, computed, watch, onMounted } from 'vue'
import { useLocationService } from '../../composables/useLocationService.js'

// ===== 组件属性定义 =====

/**
 * 组件接收的属性
 */
const props = defineProps({
  // 当前城市名称
  city: {
    type: String,
    default: ''
  },
  // 当前经度
  longitude: {
    type: [String, Number],
    default: ''
  },
  // 当前纬度
  latitude: {
    type: [String, Number],
    default: ''
  },
  // 输入框类型：start(起点) 或 end(终点)
  inputType: {
    type: String,
    default: 'start',
    validator: (value) => ['start', 'end'].includes(value)
  },
  // 输入框占位符文本
  defaultValue: {
    type: String,
    default: '请输入地点'
  }
})

/**
 * 组件事件定义
 */
const emit = defineEmits(['customEvent'])

// ===== 组合式函数 =====

// 位置服务
const { getInputTips } = useLocationService()

// ===== 响应式状态 =====

// 搜索提示列表
const tips = ref([])

// 是否显示提示列表
const showTips = ref(false)

// 输入框是否获得焦点
const isFocused = ref(false)

// 当前搜索关键词
const searchKeyword = ref('')

// 输入框防抖定时器
let searchTimer = null

// ===== 计算属性 =====

/**
 * 输入框占位符
 */
const placeholder = computed(() => {
  const typeText = props.inputType === 'start' ? '起点' : '终点'
  return props.defaultValue || `请输入${typeText}`
})

/**
 * 当前位置坐标字符串
 */
const currentLocation = computed(() => {
  if (!props.longitude || !props.latitude) return ''
  return `${props.longitude},${props.latitude}`
})

// ===== 方法定义 =====

/**
 * 处理输入框输入事件
 * @param {string|object} e - 输入事件对象或直接的值
 */
function handleInput(e) {
  // uni-easyinput 直接传递值，而不是事件对象
  const keywords = (typeof e === 'string' ? e : e.detail?.value || e.target?.value || '').trim()
  console.log('输入内容:', keywords)

  searchKeyword.value = keywords

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 如果输入为空，清空提示并通知父组件
  if (!keywords) {
    tips.value = []
    showTips.value = false

    // 向父组件发送清空事件
    emit('customEvent', {
      info: null, // 传递null表示清空
      inputType: props.inputType
    })

    return
  }

  // 防抖处理，延迟搜索
  searchTimer = setTimeout(() => {
    searchLocationTips(keywords)
  }, 300)
}

/**
 * 搜索地点提示
 * @param {string} keywords - 搜索关键词
 */
function searchLocationTips(keywords) {
  if (!keywords.trim()) return

  console.log('搜索地点提示:', keywords)

  getInputTips(
    {
      keywords: keywords.trim(),
      location: currentLocation.value,
      city: props.city
    },
    (data) => {
      console.log('搜索提示结果:', data)

      if (data && data.tips) {
        tips.value = data.tips
        showTips.value = true
      } else {
        tips.value = []
        showTips.value = true
      }
    },
    (error) => {
      console.error('搜索提示失败:', error)
      tips.value = []
      showTips.value = false
    }
  )
}

/**
 * 处理提示项选择
 * @param {object} e - 点击事件对象
 */
function handleTipSelect(e) {
  const tipInfo = e.currentTarget.dataset.info
  console.log('选择地点:', tipInfo)

  if (!tipInfo) {
    console.warn('未找到地点信息')
    return
  }

  // 更新显示值
  searchKeyword.value = tipInfo.name || ''

  // 隐藏提示列表
  showTips.value = false

  // 向父组件发送选择事件
  emit('customEvent', {
    info: tipInfo,
    inputType: props.inputType
  })
}

/**
 * 处理输入框获得焦点
 */
function handleFocus() {
  console.log('输入框获得焦点')
  isFocused.value = true

  // 如果有搜索结果，显示提示列表
  if (tips.value.length > 0) {
    showTips.value = true
  }
}

/**
 * 处理输入框失去焦点
 */
function handleBlur() {
  console.log('输入框失去焦点')
  isFocused.value = false

  // 延迟隐藏提示列表，给点击事件时间执行
  setTimeout(() => {
    showTips.value = false
  }, 200)
}

/**
 * 清除输入内容
 */
function clearInput() {
  console.log('清除输入内容')
  searchKeyword.value = ''
  tips.value = []
  showTips.value = false

  // 向父组件发送清空事件，清除对应的坐标数据
  emit('customEvent', {
    info: null, // 传递null表示清空
    inputType: props.inputType
  })
}

// ===== 监听器 =====

/**
 * 监听城市变化，更新显示值
 */
watch(() => props.city, (newCity) => {
  if (newCity && !searchKeyword.value) {
    console.log('城市更新:', newCity)
  }
})

// ===== 生命周期 =====

/**
 * 组件挂载完成
 */
onMounted(() => {
  console.log(`${props.inputType}搜索框组件已挂载`)
})
</script>

<style scoped lang="scss">
/**
 * 地点搜索输入组件样式
 * 现代化设计，支持响应式和深色模式
 */

/* 搜索输入容器 */
.search-input-container {
  position: relative; /* 必须保留，用于下拉提示定位 */
  width: 100%;
  /* 移除padding，由父容器统一管理 */
}

/* 输入框包装器 */
.input-wrapper {
  width: 100%;
}

/* uni-easyinput 样式覆盖 */
.search-input {
  width: 100%;

  /* 覆盖uni-easyinput的默认样式 */
  :deep(.uni-easyinput) {
    background: #ffffff;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #0091ff;
      box-shadow: 0 0 0 3px rgba(0, 145, 255, 0.1);
    }
  }

  :deep(.uni-easyinput__content) {
    height: 44px;
    padding: 0 16px;
  }

  :deep(.uni-easyinput__content-input) {
    font-size: 16px;
    color: #333;

    &::placeholder {
      color: #999;
      font-size: 14px;
    }
  }

  :deep(.uni-easyinput__content-clear-btn) {
    color: #666;
    font-size: 16px;
  }
}

/* 输入框图标 */
.input-icon {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.icon-location {
  font-size: 16px;
  color: #0091ff;
}



/* 提示列表容器 */
.tips-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;

    &:hover {
      background: #a1a1a1;
    }
  }
}

/* 提示项 */
.tip-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f8f9fa;
  }

  &:active {
    background: #e9ecef;
  }
}

/* 提示内容 */
.tip-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 地点名称 */
.tip-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

/* 地点地址 */
.tip-address {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

/* 箭头图标 */
.tip-arrow {
  color: #ccc;
  font-size: 14px;
  margin-left: 8px;
}

/* 无结果提示 */
.no-results {
  padding: 20px;
  text-align: center;
}

.no-results-text {
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .search-input {
    height: 40px;
    font-size: 14px;
  }

  .tip-item {
    padding: 10px 12px;
  }

  .tip-name {
    font-size: 14px;
  }

  .tip-address {
    font-size: 11px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .search-input {
    :deep(.uni-easyinput) {
      background: #2c2c2c;
      border-color: #404040;
    }

    :deep(.uni-easyinput__content-input) {
      color: #e0e0e0;

      &::placeholder {
        color: #888;
      }
    }

    :deep(.uni-easyinput__content-clear-btn) {
      color: #ccc;
    }
  }

  .icon-location {
    color: #66b3ff;
  }

  .tips-container {
    background: #2c2c2c;
    border-color: #404040;
  }

  .tip-item {
    border-bottom-color: #404040;

    &:hover {
      background: #383838;
    }

    &:active {
      background: #444;
    }
  }

  .tip-name {
    color: #e0e0e0;
  }

  .tip-address {
    color: #aaa;
  }

  .tip-arrow {
    color: #666;
  }

  .no-results-text {
    color: #888;
  }
}
</style>