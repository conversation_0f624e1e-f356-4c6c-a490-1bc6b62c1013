"use strict";const b=require("../libs/amap-wx.130.js"),_=require("../libs/config.js"),c=require("../utils/mapUtils.js");function P(){function g(){return new b.AMapWX({key:_.gaode_key.config.key})}function h(i,s,n){const{origin:o,destination:a}=i;if(!o||!a){console.warn("驾车路线规划缺少必要参数");return}g().getDrivingRoute({origin:o,destination:a,success:function(t){var e,r,u,l;console.log("驾车路线数据:",t);try{const p=c.parseRoutePoints(((r=(e=t.paths)==null?void 0:e[0])==null?void 0:r.steps)||[]),y=c.createPolyline(p),f={distance:(l=(u=t.paths)==null?void 0:u[0])!=null&&l.distance?c.formatDistance(t.paths[0].distance):"",cost:t.taxi_cost?c.formatTaxiCost(t.taxi_cost):"",polyline:y};s&&s(f)}catch(p){console.error("驾车路线数据处理失败:",p),n&&n(p)}},fail:function(t){console.error("驾车路线规划失败:",t),c.handleMapError("驾车路线规划",t),n&&n(t)}})}function R(i,s,n){const{origin:o,destination:a}=i;if(!o||!a){console.warn("步行路线规划缺少必要参数");return}g().getWalkingRoute({origin:o,destination:a,success:function(t){var e,r,u,l,p,y;console.log("步行路线数据:",t);try{const f=c.parseRoutePoints(((r=(e=t.paths)==null?void 0:e[0])==null?void 0:r.steps)||[]),w=c.createPolyline(f),A={distance:(l=(u=t.paths)==null?void 0:u[0])!=null&&l.distance?c.formatDistance(t.paths[0].distance):"",cost:(y=(p=t.paths)==null?void 0:p[0])!=null&&y.duration?c.formatDuration(t.paths[0].duration):"",polyline:w};s&&s(A)}catch(f){console.error("步行路线数据处理失败:",f),n&&n(f)}},fail:function(t){console.error("步行路线规划失败:",t),c.handleMapError("步行路线规划",t),n&&n(t)}})}function d(i,s,n){const{origin:o,destination:a,city:m="广州"}=i;if(!o||!a){console.warn("公交路线规划缺少必要参数");return}g().getTransitRoute({origin:o,destination:a,city:m,success:function(e){console.log("公交路线数据:",e);try{if(e&&e.transits){const r=c.processTransitData(e.transits);s&&s({transits:r})}else console.warn("公交路线数据为空"),n&&n({message:"未找到公交路线"})}catch(r){console.error("公交路线数据处理失败:",r),n&&n(r)}},fail:function(e){console.error("公交路线规划失败:",e),c.handleMapError("公交路线规划",e),n&&n(e)}})}function D(i,s,n){const{origin:o,destination:a}=i;if(!o||!a){console.warn("骑行路线规划缺少必要参数");return}g().getRidingRoute({origin:o,destination:a,success:function(t){console.log("骑行路线数据:",t);try{if(t&&t.paths&&t.paths[0]){const e=t.paths[0],r=c.parseRidingPoints(e.rides||[]),u=c.createPolyline(r),l={distance:e.distance?c.formatDistance(e.distance):"",cost:e.duration?c.formatDuration(e.duration):"",polyline:u};s&&s(l)}else console.warn("骑行路线数据为空"),n&&n({message:"未找到骑行路线"})}catch(e){console.error("骑行路线数据处理失败:",e),n&&n(e)}},fail:function(t){console.error("骑行路线规划失败:",t),c.handleMapError("骑行路线规划",t),n&&n(t)}})}function k(i,s,n,o){switch(console.log(`开始${i}路线规划:`,s),i){case"car":h(s,n,o);break;case"walk":R(s,n,o);break;case"bus":d(s,n,o);break;case"riding":D(s,n,o);break;default:console.warn("不支持的路线类型:",i),o&&o({message:"不支持的路线类型"})}}return{getDrivingRoute:h,getWalkingRoute:R,getTransitRoute:d,getRidingRoute:D,planRoute:k}}exports.useRouteService=P;
